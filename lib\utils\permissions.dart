import 'dart:io';
import 'package:permission_handler/permission_handler.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bluetooth_serial/flutter_bluetooth_serial.dart';

class PermissionManager {
  static Future<bool> checkBluetoothAvailability() async {
    try {
      final bluetoothState = await FlutterBluetoothSerial.instance.state;
      return bluetoothState != BluetoothState.UNKNOWN;
    } catch (e) {
      debugPrint('Error checking Bluetooth availability: $e');
      return false;
    }
  }

  static Future<bool> isBluetoothEnabled() async {
    try {
      final bluetoothState = await FlutterBluetoothSerial.instance.state;
      return bluetoothState == BluetoothState.STATE_ON;
    } catch (e) {
      debugPrint('Error checking Bluetooth state: $e');
      return false;
    }
  }

  static Future<bool> enableBluetooth() async {
    try {
      final result = await FlutterBluetoothSerial.instance.requestEnable();
      return result ?? false;
    } catch (e) {
      debugPrint('Error enabling Bluetooth: $e');
      return false;
    }
  }

  static Future<bool> requestBluetoothPermissions() async {
    try {
      Map<Permission, PermissionStatus> permissions = {};

      if (Platform.isAndroid) {
        // Check Android version for appropriate permissions
        final androidInfo = await _getAndroidVersion();

        if (androidInfo >= 31) {
          // Android 12+ (API 31+) permissions
          permissions = await [
            Permission.bluetoothScan,
            Permission.bluetoothConnect,
            Permission.bluetoothAdvertise,
            Permission.locationWhenInUse,
          ].request();
        } else {
          // Pre-Android 12 permissions
          permissions = await [
            Permission.location,
            Permission.locationWhenInUse,
          ].request();
        }
      } else if (Platform.isIOS) {
        // iOS permissions
        permissions = await [
          Permission.bluetooth,
          Permission.locationWhenInUse,
        ].request();
      }

      // Check if all permissions are granted
      return permissions.values.every(
        (status) => status == PermissionStatus.granted,
      );
    } catch (e) {
      debugPrint('Error requesting Bluetooth permissions: $e');
      return false;
    }
  }

  static Future<bool> requestLocationPermission() async {
    try {
      final status = await Permission.locationWhenInUse.request();
      return status == PermissionStatus.granted;
    } catch (e) {
      debugPrint('Error requesting location permission: $e');
      return false;
    }
  }

  static Future<bool> requestNotificationPermission() async {
    try {
      final status = await Permission.notification.request();
      return status == PermissionStatus.granted;
    } catch (e) {
      debugPrint('Error requesting notification permission: $e');
      return false;
    }
  }

  static Future<bool> requestStoragePermission() async {
    try {
      if (Platform.isAndroid) {
        final androidInfo = await _getAndroidVersion();

        if (androidInfo >= 33) {
          // Android 13+ uses scoped storage
          return true;
        } else {
          final status = await Permission.storage.request();
          return status == PermissionStatus.granted;
        }
      }
      return true; // iOS doesn't need explicit storage permission for app documents
    } catch (e) {
      debugPrint('Error requesting storage permission: $e');
      return false;
    }
  }

  static Future<bool> checkAllPermissions() async {
    try {
      final bluetoothAvailable = await checkBluetoothAvailability();
      if (!bluetoothAvailable) return false;

      final bluetoothEnabled = await isBluetoothEnabled();
      if (!bluetoothEnabled) return false;

      // Check required permissions based on platform
      if (Platform.isAndroid) {
        final androidInfo = await _getAndroidVersion();

        if (androidInfo >= 31) {
          // Android 12+ permissions
          final permissions = await [
            Permission.bluetoothScan,
            Permission.bluetoothConnect,
            Permission.locationWhenInUse,
          ].request();

          return permissions.values.every(
            (status) => status == PermissionStatus.granted,
          );
        } else {
          // Pre-Android 12 permissions
          final locationStatus = await Permission.locationWhenInUse.status;
          return locationStatus == PermissionStatus.granted;
        }
      } else if (Platform.isIOS) {
        final bluetoothStatus = await Permission.bluetooth.status;
        final locationStatus = await Permission.locationWhenInUse.status;

        return bluetoothStatus == PermissionStatus.granted &&
            locationStatus == PermissionStatus.granted;
      }

      return false;
    } catch (e) {
      debugPrint('Error checking all permissions: $e');
      return false;
    }
  }

  static Future<PermissionStatus> getBluetoothPermissionStatus() async {
    try {
      if (Platform.isAndroid) {
        final androidInfo = await _getAndroidVersion();

        if (androidInfo >= 31) {
          return await Permission.bluetoothScan.status;
        } else {
          return await Permission.location.status;
        }
      } else if (Platform.isIOS) {
        return await Permission.bluetooth.status;
      }

      return PermissionStatus.denied;
    } catch (e) {
      debugPrint('Error getting Bluetooth permission status: $e');
      return PermissionStatus.denied;
    }
  }

  static Future<void> openAppSettings() async {
    try {
      await openAppSettings();
    } catch (e) {
      debugPrint('Error opening app settings: $e');
    }
  }

  static Future<int> _getAndroidVersion() async {
    // This is a simplified version. In a real app, you might want to use
    // device_info_plus package to get accurate Android version
    return 31; // Assume Android 12+ for now
  }

  static String getPermissionExplanation(Permission permission) {
    switch (permission) {
      case Permission.bluetooth:
      case Permission.bluetoothScan:
      case Permission.bluetoothConnect:
        return 'Bluetooth permission is required to scan for and connect to Bluetooth devices.';
      case Permission.location:
      case Permission.locationWhenInUse:
        return 'Location permission is required for Bluetooth device scanning on Android.';
      case Permission.notification:
        return 'Notification permission allows the app to show alarm notifications.';
      case Permission.storage:
        return 'Storage permission is needed to save custom alarm sounds.';
      default:
        return 'This permission is required for the app to function properly.';
    }
  }

  static Future<bool> shouldShowRequestPermissionRationale(
      Permission permission) async {
    try {
      return await permission.shouldShowRequestRationale;
    } catch (e) {
      debugPrint('Error checking permission rationale: $e');
      return false;
    }
  }
}

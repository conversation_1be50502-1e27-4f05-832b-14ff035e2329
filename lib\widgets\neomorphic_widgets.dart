import 'package:flutter/material.dart';
import '../utils/constants.dart';

class NeomorphicContainer extends StatelessWidget {
  final Widget child;
  final double? width;
  final double? height;
  final EdgeInsetsGeometry? padding;
  final EdgeInsetsGeometry? margin;
  final bool isPressed;
  final VoidCallback? onTap;
  final BorderRadius? borderRadius;

  const NeomorphicContainer({
    super.key,
    required this.child,
    this.width,
    this.height,
    this.padding,
    this.margin,
    this.isPressed = false,
    this.onTap,
    this.borderRadius,
  });

  @override
  Widget build(BuildContext context) {
    final isDark = Theme.of(context).brightness == Brightness.dark;
    final backgroundColor = isDark
        ? AppConstants.darkBackgroundColor
        : AppConstants.lightBackgroundColor;

    final shadowColor =
        isDark ? AppConstants.darkShadowColor : AppConstants.lightShadowColor;

    final highlightColor = isDark
        ? AppConstants.darkHighlightColor
        : AppConstants.lightHighlightColor;

    return GestureDetector(
      onTap: onTap,
      child: AnimatedContainer(
        duration: AppConstants.shortAnimation,
        width: width,
        height: height,
        padding: padding ?? const EdgeInsets.all(AppConstants.defaultPadding),
        margin: margin,
        decoration: BoxDecoration(
          color: backgroundColor,
          borderRadius:
              borderRadius ?? BorderRadius.circular(AppConstants.borderRadius),
          boxShadow: isPressed
              ? [
                  BoxShadow(
                    color: shadowColor,
                    offset: const Offset(2, 2),
                    blurRadius: 5,
                    spreadRadius: 1,
                  ),
                  BoxShadow(
                    color: highlightColor,
                    offset: const Offset(-2, -2),
                    blurRadius: 5,
                    spreadRadius: 1,
                  ),
                ]
              : [
                  BoxShadow(
                    color: shadowColor,
                    offset: AppConstants.neomorphicOffset,
                    blurRadius: AppConstants.neomorphicBlurRadius,
                    spreadRadius: AppConstants.neomorphicSpreadRadius,
                  ),
                  BoxShadow(
                    color: highlightColor,
                    offset: AppConstants.neomorphicOffsetReverse,
                    blurRadius: AppConstants.neomorphicBlurRadius,
                    spreadRadius: AppConstants.neomorphicSpreadRadius,
                  ),
                ],
        ),
        child: child,
      ),
    );
  }
}

class NeomorphicButton extends StatefulWidget {
  final Widget child;
  final VoidCallback? onPressed;
  final double? width;
  final double? height;
  final EdgeInsetsGeometry? padding;
  final BorderRadius? borderRadius;
  final Color? color;

  const NeomorphicButton({
    super.key,
    required this.child,
    this.onPressed,
    this.width,
    this.height,
    this.padding,
    this.borderRadius,
    this.color,
  });

  @override
  State<NeomorphicButton> createState() => _NeomorphicButtonState();
}

class _NeomorphicButtonState extends State<NeomorphicButton> {
  bool _isPressed = false;

  @override
  Widget build(BuildContext context) {
    return GestureDetector(
      onTapDown: (_) => setState(() => _isPressed = true),
      onTapUp: (_) => setState(() => _isPressed = false),
      onTapCancel: () => setState(() => _isPressed = false),
      onTap: widget.onPressed,
      child: NeomorphicContainer(
        width: widget.width,
        height: widget.height,
        padding: widget.padding ??
            const EdgeInsets.symmetric(
              horizontal: AppConstants.defaultPadding,
              vertical: AppConstants.smallPadding,
            ),
        borderRadius: widget.borderRadius,
        isPressed: _isPressed,
        child: widget.child,
      ),
    );
  }
}

class NeomorphicCard extends StatelessWidget {
  final Widget child;
  final EdgeInsetsGeometry? padding;
  final EdgeInsetsGeometry? margin;
  final VoidCallback? onTap;

  const NeomorphicCard({
    super.key,
    required this.child,
    this.padding,
    this.margin,
    this.onTap,
  });

  @override
  Widget build(BuildContext context) {
    return NeomorphicContainer(
      padding: padding ?? const EdgeInsets.all(AppConstants.defaultPadding),
      margin: margin ??
          const EdgeInsets.symmetric(
            horizontal: AppConstants.defaultPadding,
            vertical: AppConstants.smallPadding,
          ),
      onTap: onTap,
      child: child,
    );
  }
}

class NeomorphicSwitch extends StatelessWidget {
  final bool value;
  final ValueChanged<bool>? onChanged;
  final double width;
  final double height;

  const NeomorphicSwitch({
    super.key,
    required this.value,
    this.onChanged,
    this.width = 60,
    this.height = 30,
  });

  @override
  Widget build(BuildContext context) {
    final isDark = Theme.of(context).brightness == Brightness.dark;
    final backgroundColor = isDark
        ? AppConstants.darkBackgroundColor
        : AppConstants.lightBackgroundColor;

    return GestureDetector(
      onTap: () => onChanged?.call(!value),
      child: AnimatedContainer(
        duration: AppConstants.mediumAnimation,
        width: width,
        height: height,
        decoration: BoxDecoration(
          color: backgroundColor,
          borderRadius: BorderRadius.circular(height / 2),
          boxShadow: [
            BoxShadow(
              color: isDark
                  ? AppConstants.darkShadowColor
                  : AppConstants.lightShadowColor,
              offset: const Offset(2, 2),
              blurRadius: 4,
            ),
            BoxShadow(
              color: isDark
                  ? AppConstants.darkHighlightColor
                  : AppConstants.lightHighlightColor,
              offset: const Offset(-2, -2),
              blurRadius: 4,
            ),
          ],
        ),
        child: Stack(
          children: [
            AnimatedPositioned(
              duration: AppConstants.mediumAnimation,
              left: value ? width - height + 4 : 4,
              top: 4,
              child: Container(
                width: height - 8,
                height: height - 8,
                decoration: BoxDecoration(
                  color: value ? AppConstants.primaryAccent : Colors.grey,
                  shape: BoxShape.circle,
                  boxShadow: [
                    BoxShadow(
                      color: Colors.black.withValues(alpha: 0.2),
                      offset: const Offset(1, 1),
                      blurRadius: 2,
                    ),
                  ],
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }
}

class NeomorphicSlider extends StatelessWidget {
  final double value;
  final ValueChanged<double>? onChanged;
  final double min;
  final double max;
  final int? divisions;

  const NeomorphicSlider({
    super.key,
    required this.value,
    this.onChanged,
    this.min = 0.0,
    this.max = 1.0,
    this.divisions,
  });

  @override
  Widget build(BuildContext context) {
    final isDark = Theme.of(context).brightness == Brightness.dark;

    return NeomorphicContainer(
      padding:
          const EdgeInsets.symmetric(horizontal: AppConstants.smallPadding),
      child: SliderTheme(
        data: SliderTheme.of(context).copyWith(
          activeTrackColor: AppConstants.primaryAccent,
          inactiveTrackColor: isDark
              ? AppConstants.darkShadowColor
              : AppConstants.lightShadowColor,
          thumbColor: AppConstants.primaryAccent,
          overlayColor: AppConstants.primaryAccent.withValues(alpha: 0.2),
          thumbShape: const RoundSliderThumbShape(enabledThumbRadius: 12),
          trackHeight: 6,
        ),
        child: Slider(
          value: value,
          onChanged: onChanged,
          min: min,
          max: max,
          divisions: divisions,
        ),
      ),
    );
  }
}

class NeomorphicIconButton extends StatefulWidget {
  final IconData icon;
  final VoidCallback? onPressed;
  final Color? iconColor;
  final double? iconSize;
  final double? size;

  const NeomorphicIconButton({
    super.key,
    required this.icon,
    this.onPressed,
    this.iconColor,
    this.iconSize,
    this.size,
  });

  @override
  State<NeomorphicIconButton> createState() => _NeomorphicIconButtonState();
}

class _NeomorphicIconButtonState extends State<NeomorphicIconButton> {
  bool _isPressed = false;

  @override
  Widget build(BuildContext context) {
    final size = widget.size ?? 48.0;

    return GestureDetector(
      onTapDown: (_) => setState(() => _isPressed = true),
      onTapUp: (_) => setState(() => _isPressed = false),
      onTapCancel: () => setState(() => _isPressed = false),
      onTap: widget.onPressed,
      child: NeomorphicContainer(
        width: size,
        height: size,
        padding: EdgeInsets.zero,
        borderRadius: BorderRadius.circular(size / 2),
        isPressed: _isPressed,
        child: Icon(
          widget.icon,
          color: widget.iconColor ?? AppConstants.primaryAccent,
          size: widget.iconSize ?? AppConstants.mediumIconSize,
        ),
      ),
    );
  }
}

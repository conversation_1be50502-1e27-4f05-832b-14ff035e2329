import 'package:flutter_bluetooth_serial/flutter_bluetooth_serial.dart';

enum DeviceConnectionStatus {
  connected,
  disconnected,
  connecting,
  unknown,
}

class BluetoothDeviceModel {
  final String name;
  final String address;
  final DeviceConnectionStatus connectionStatus;
  final bool isPaired;
  final int? rssi;
  final DateTime lastSeen;
  final BluetoothDevice? bluetoothDevice;

  BluetoothDeviceModel({
    required this.name,
    required this.address,
    this.connectionStatus = DeviceConnectionStatus.unknown,
    this.isPaired = false,
    this.rssi,
    DateTime? lastSeen,
    this.bluetoothDevice,
  }) : lastSeen = lastSeen ?? DateTime.now();

  // Create from BluetoothDevice
  factory BluetoothDeviceModel.fromBluetoothDevice(
    BluetoothDevice device, {
    DeviceConnectionStatus? status,
    int? rssi,
  }) {
    return BluetoothDeviceModel(
      name: device.name ?? 'Unknown Device',
      address: device.address,
      connectionStatus: status ?? DeviceConnectionStatus.unknown,
      isPaired: device.isBonded,
      rssi: rssi,
      bluetoothDevice: device,
    );
  }

  // Create from BluetoothDiscoveryResult
  factory BluetoothDeviceModel.fromDiscoveryResult(
    BluetoothDiscoveryResult result,
  ) {
    return BluetoothDeviceModel(
      name: result.device.name ?? 'Unknown Device',
      address: result.device.address,
      connectionStatus: DeviceConnectionStatus.disconnected,
      isPaired: result.device.isBonded,
      rssi: result.rssi,
      bluetoothDevice: result.device,
    );
  }

  BluetoothDeviceModel copyWith({
    String? name,
    String? address,
    DeviceConnectionStatus? connectionStatus,
    bool? isPaired,
    int? rssi,
    DateTime? lastSeen,
    BluetoothDevice? bluetoothDevice,
  }) {
    return BluetoothDeviceModel(
      name: name ?? this.name,
      address: address ?? this.address,
      connectionStatus: connectionStatus ?? this.connectionStatus,
      isPaired: isPaired ?? this.isPaired,
      rssi: rssi ?? this.rssi,
      lastSeen: lastSeen ?? this.lastSeen,
      bluetoothDevice: bluetoothDevice ?? this.bluetoothDevice,
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'name': name,
      'address': address,
      'connectionStatus': connectionStatus.index,
      'isPaired': isPaired,
      'rssi': rssi,
      'lastSeen': lastSeen.millisecondsSinceEpoch,
    };
  }

  factory BluetoothDeviceModel.fromJson(Map<String, dynamic> json) {
    return BluetoothDeviceModel(
      name: json['name'] ?? 'Unknown Device',
      address: json['address'] ?? '',
      connectionStatus: DeviceConnectionStatus.values[json['connectionStatus'] ?? 0],
      isPaired: json['isPaired'] ?? false,
      rssi: json['rssi'],
      lastSeen: DateTime.fromMillisecondsSinceEpoch(json['lastSeen'] ?? 0),
    );
  }

  @override
  bool operator ==(Object other) {
    if (identical(this, other)) return true;
    return other is BluetoothDeviceModel && other.address == address;
  }

  @override
  int get hashCode => address.hashCode;

  @override
  String toString() {
    return 'BluetoothDeviceModel(name: $name, address: $address, status: $connectionStatus)';
  }

  String get displayName {
    return name.isNotEmpty ? name : 'Unknown Device';
  }

  String get statusText {
    switch (connectionStatus) {
      case DeviceConnectionStatus.connected:
        return 'Connected';
      case DeviceConnectionStatus.disconnected:
        return 'Disconnected';
      case DeviceConnectionStatus.connecting:
        return 'Connecting...';
      case DeviceConnectionStatus.unknown:
        return 'Unknown';
    }
  }

  bool get isConnected => connectionStatus == DeviceConnectionStatus.connected;
  bool get isDisconnected => connectionStatus == DeviceConnectionStatus.disconnected;
}

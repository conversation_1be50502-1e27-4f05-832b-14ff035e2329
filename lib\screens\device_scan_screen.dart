import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
// import 'package:flutter_staggered_animations/flutter_staggered_animations.dart';
import '../models/bluetooth_device_model.dart';
import '../services/bluetooth_service.dart';
import '../widgets/neomorphic_widgets.dart';
import '../utils/constants.dart';
import '../utils/permissions.dart';

class DeviceScanScreen extends StatefulWidget {
  const DeviceScanScreen({super.key});

  @override
  State<DeviceScanScreen> createState() => _DeviceScanScreenState();
}

class _DeviceScanScreenState extends State<DeviceScanScreen> {
  late BluetoothService _bluetoothService;
  bool _hasPermissions = false;
  String? _errorMessage;

  @override
  void initState() {
    super.initState();
    _bluetoothService = context.read<BluetoothService>();
    _checkPermissions();
  }

  Future<void> _checkPermissions() async {
    try {
      final hasPermissions = await PermissionManager.checkAllPermissions();
      setState(() {
        _hasPermissions = hasPermissions;
      });

      if (!hasPermissions) {
        _showPermissionDialog();
      }
    } catch (e) {
      setState(() {
        _errorMessage = 'Error checking permissions: $e';
      });
    }
  }

  @override
  Widget build(BuildContext context) {
    final isDark = Theme.of(context).brightness == Brightness.dark;

    return Scaffold(
      backgroundColor: isDark
          ? AppConstants.darkBackgroundColor
          : AppConstants.lightBackgroundColor,
      appBar: _buildAppBar(),
      body: SafeArea(
        child: Column(
          children: [
            _buildScanControls(),
            if (_errorMessage != null) _buildErrorMessage(),
            Expanded(child: _buildDeviceList()),
          ],
        ),
      ),
    );
  }

  PreferredSizeWidget _buildAppBar() {
    return AppBar(
      backgroundColor: Colors.transparent,
      elevation: 0,
      leading: NeomorphicIconButton(
        icon: Icons.arrow_back,
        onPressed: () => Navigator.of(context).pop(),
      ),
      title: Text(
        'Scan for Devices',
        style: AppConstants.headingStyle.copyWith(
          color: AppConstants.primaryAccent,
        ),
      ),
      centerTitle: true,
    );
  }

  Widget _buildScanControls() {
    return NeomorphicContainer(
      margin: const EdgeInsets.all(AppConstants.defaultPadding),
      child: Column(
        children: [
          Row(
            children: [
              Icon(
                Icons.bluetooth_searching,
                color: AppConstants.primaryAccent,
                size: AppConstants.largeIconSize,
              ),
              const SizedBox(width: AppConstants.defaultPadding),
              Expanded(
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      'Bluetooth Scanning',
                      style: AppConstants.subheadingStyle,
                    ),
                    Consumer<BluetoothService>(
                      builder: (context, service, _) {
                        return Text(
                          service.isScanning
                              ? 'Scanning for nearby devices...'
                              : 'Tap scan to find devices',
                          style: AppConstants.captionStyle,
                        );
                      },
                    ),
                  ],
                ),
              ),
            ],
          ),
          const SizedBox(height: AppConstants.defaultPadding),
          Row(
            children: [
              Expanded(
                child: Consumer<BluetoothService>(
                  builder: (context, service, _) {
                    return NeomorphicButton(
                      onPressed: _hasPermissions
                          ? (service.isScanning ? _stopScan : _startScan)
                          : _requestPermissions,
                      child: Row(
                        mainAxisAlignment: MainAxisAlignment.center,
                        children: [
                          if (service.isScanning)
                            const SizedBox(
                              width: 16,
                              height: 16,
                              child: CircularProgressIndicator(
                                strokeWidth: 2,
                                color: AppConstants.primaryAccent,
                              ),
                            )
                          else
                            Icon(
                              _hasPermissions ? Icons.search : Icons.security,
                              color: AppConstants.primaryAccent,
                            ),
                          const SizedBox(width: AppConstants.smallPadding),
                          Text(
                            _hasPermissions
                                ? (service.isScanning
                                    ? 'Stop Scan'
                                    : 'Start Scan')
                                : 'Grant Permissions',
                            style: AppConstants.bodyStyle.copyWith(
                              color: AppConstants.primaryAccent,
                              fontWeight: FontWeight.w600,
                            ),
                          ),
                        ],
                      ),
                    );
                  },
                ),
              ),
              const SizedBox(width: AppConstants.smallPadding),
              NeomorphicIconButton(
                icon: Icons.refresh,
                onPressed: _refreshPairedDevices,
              ),
            ],
          ),
        ],
      ),
    );
  }

  Widget _buildErrorMessage() {
    return Container(
      margin:
          const EdgeInsets.symmetric(horizontal: AppConstants.defaultPadding),
      padding: const EdgeInsets.all(AppConstants.defaultPadding),
      decoration: BoxDecoration(
        color: AppConstants.errorColor.withValues(alpha: 0.1),
        borderRadius: BorderRadius.circular(AppConstants.borderRadius),
      ),
      child: Row(
        children: [
          Icon(
            Icons.error_outline,
            color: AppConstants.errorColor,
          ),
          const SizedBox(width: AppConstants.smallPadding),
          Expanded(
            child: Text(
              _errorMessage!,
              style: AppConstants.captionStyle.copyWith(
                color: AppConstants.errorColor,
              ),
            ),
          ),
          IconButton(
            icon: const Icon(Icons.close),
            onPressed: () => setState(() => _errorMessage = null),
          ),
        ],
      ),
    );
  }

  Widget _buildDeviceList() {
    return Consumer<BluetoothService>(
      builder: (context, service, _) {
        final allDevices = [
          ...service.pairedDevices,
          ...service.devices.where((device) => !service.pairedDevices
              .any((paired) => paired.address == device.address)),
        ];

        if (allDevices.isEmpty && !service.isScanning) {
          return _buildEmptyState();
        }

        return ListView.builder(
          padding: const EdgeInsets.all(AppConstants.defaultPadding),
          itemCount: allDevices.length,
          itemBuilder: (context, index) {
            return _buildDeviceCard(allDevices[index]);
          },
        );
      },
    );
  }

  Widget _buildDeviceCard(BluetoothDeviceModel device) {
    return NeomorphicCard(
      child: Row(
        children: [
          Container(
            padding: const EdgeInsets.all(AppConstants.smallPadding),
            decoration: BoxDecoration(
              color: _getStatusColor(device).withValues(alpha: 0.1),
              borderRadius: BorderRadius.circular(AppConstants.borderRadius),
            ),
            child: Icon(
              device.displayName.deviceIcon,
              color: _getStatusColor(device),
              size: AppConstants.largeIconSize,
            ),
          ),
          const SizedBox(width: AppConstants.defaultPadding),
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  device.displayName,
                  style: AppConstants.bodyStyle.copyWith(
                    fontWeight: FontWeight.w600,
                  ),
                ),
                const SizedBox(height: AppConstants.smallPadding / 2),
                Text(
                  device.address,
                  style: AppConstants.captionStyle,
                ),
                const SizedBox(height: AppConstants.smallPadding / 2),
                Row(
                  children: [
                    Container(
                      padding: const EdgeInsets.symmetric(
                        horizontal: AppConstants.smallPadding,
                        vertical: 2,
                      ),
                      decoration: BoxDecoration(
                        color: _getStatusColor(device).withValues(alpha: 0.2),
                        borderRadius: BorderRadius.circular(12),
                      ),
                      child: Text(
                        device.isPaired ? 'Paired' : device.statusText,
                        style: AppConstants.captionStyle.copyWith(
                          color: _getStatusColor(device),
                          fontSize: 12,
                        ),
                      ),
                    ),
                    if (device.rssi != null) ...[
                      const SizedBox(width: AppConstants.smallPadding),
                      Icon(
                        Icons.signal_cellular_alt,
                        size: AppConstants.smallIconSize,
                        color: _getRssiColor(device.rssi!),
                      ),
                      Text(
                        '${device.rssi}dBm',
                        style: AppConstants.captionStyle.copyWith(
                          color: _getRssiColor(device.rssi!),
                          fontSize: 12,
                        ),
                      ),
                    ],
                  ],
                ),
              ],
            ),
          ),
          Column(
            children: [
              if (!device.isPaired)
                NeomorphicButton(
                  onPressed: () => _pairDevice(device),
                  padding: const EdgeInsets.symmetric(
                    horizontal: AppConstants.smallPadding,
                    vertical: AppConstants.smallPadding / 2,
                  ),
                  child: Text(
                    'Pair',
                    style: AppConstants.captionStyle.copyWith(
                      color: AppConstants.primaryAccent,
                      fontWeight: FontWeight.w600,
                    ),
                  ),
                )
              else
                NeomorphicIconButton(
                  icon: device.isConnected ? Icons.link_off : Icons.link,
                  iconColor: device.isConnected
                      ? AppConstants.disconnectedColor
                      : AppConstants.connectedColor,
                  size: 40,
                  onPressed: () => _toggleConnection(device),
                ),
            ],
          ),
        ],
      ),
    );
  }

  Widget _buildEmptyState() {
    return Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Icon(
            Icons.bluetooth_disabled,
            size: 64,
            color: AppConstants.unknownColor,
          ),
          const SizedBox(height: AppConstants.defaultPadding),
          Text(
            'No Devices Found',
            style: AppConstants.subheadingStyle.copyWith(
              color: AppConstants.unknownColor,
            ),
          ),
          const SizedBox(height: AppConstants.smallPadding),
          Text(
            'Make sure Bluetooth is enabled and devices are discoverable',
            style: AppConstants.captionStyle,
            textAlign: TextAlign.center,
          ),
        ],
      ),
    );
  }

  Color _getStatusColor(BluetoothDeviceModel device) {
    if (device.isPaired) {
      return device.isConnected
          ? AppConstants.connectedColor
          : AppConstants.disconnectedColor;
    }
    return AppConstants.primaryAccent;
  }

  Color _getRssiColor(int rssi) {
    if (rssi > -50) return AppConstants.connectedColor;
    if (rssi > -70) return AppConstants.connectingColor;
    return AppConstants.disconnectedColor;
  }

  Future<void> _startScan() async {
    try {
      setState(() => _errorMessage = null);
      final success = await _bluetoothService.startScan();
      if (!success) {
        setState(() => _errorMessage = 'Failed to start scanning');
      }
    } catch (e) {
      setState(() => _errorMessage = 'Scan error: $e');
    }
  }

  void _stopScan() {
    _bluetoothService.stopScan();
  }

  Future<void> _requestPermissions() async {
    final granted = await PermissionManager.requestBluetoothPermissions();
    setState(() => _hasPermissions = granted);

    if (!granted) {
      _showPermissionDialog();
    }
  }

  Future<void> _refreshPairedDevices() async {
    await _bluetoothService.initialize();
  }

  Future<void> _pairDevice(BluetoothDeviceModel device) async {
    try {
      final success = await _bluetoothService.pairWithDevice(device);
      if (!mounted) return;

      if (success) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(content: Text('Paired with ${device.displayName}')),
        );
      } else {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(content: Text('Failed to pair with ${device.displayName}')),
        );
      }
    } catch (e) {
      if (!mounted) return;
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(content: Text('Error pairing: $e')),
      );
    }
  }

  Future<void> _toggleConnection(BluetoothDeviceModel device) async {
    try {
      if (device.isConnected) {
        // In a real app, you would disconnect here
        if (!mounted) return;
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(content: Text('Disconnected from ${device.displayName}')),
        );
      } else {
        final success = await _bluetoothService.connectToDevice(device);
        if (!mounted) return;
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text(
              success
                  ? 'Connected to ${device.displayName}'
                  : 'Failed to connect to ${device.displayName}',
            ),
          ),
        );
      }
    } catch (e) {
      if (!mounted) return;
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(content: Text('Connection error: $e')),
      );
    }
  }

  void _showPermissionDialog() {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('Permissions Required'),
        content: const Text(
          'Bluetooth and location permissions are required to scan for devices. '
          'Please grant these permissions in the app settings.',
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(),
            child: const Text('Cancel'),
          ),
          TextButton(
            onPressed: () {
              Navigator.of(context).pop();
              PermissionManager.openAppSettings();
            },
            child: const Text('Settings'),
          ),
        ],
      ),
    );
  }
}

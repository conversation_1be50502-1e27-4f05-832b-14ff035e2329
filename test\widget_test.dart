// This is a basic Flutter widget test.
//
// To perform an interaction with a widget in your test, use the WidgetTester
// utility in the flutter_test package. For example, you can send tap and scroll
// gestures. You can also use WidgetTester to find child widgets in the widget
// tree, read text, and verify that the values of widget properties are correct.

import 'package:flutter/material.dart';
import 'package:flutter_test/flutter_test.dart';
import 'package:provider/provider.dart';

import 'package:flutter_application_3/main.dart';
import 'package:flutter_application_3/services/bluetooth_service.dart';
import 'package:flutter_application_3/services/alarm_service.dart';
import 'package:flutter_application_3/screens/home_screen.dart';
import 'package:flutter_application_3/widgets/neomorphic_widgets.dart';

void main() {
  testWidgets('Neomorphic widgets test', (WidgetTester tester) async {
    // Test individual neomorphic widgets
    await tester.pumpWidget(
      MaterialApp(
        home: Scaffold(
          body: Column(
            children: [
              NeomorphicContainer(
                child: Text('Test Container'),
              ),
              NeomorphicButton(
                onPressed: () {},
                child: Text('Test Button'),
              ),
              NeomorphicSwitch(
                value: true,
                onChanged: (value) {},
              ),
            ],
          ),
        ),
      ),
    );

    // Verify that the widgets are rendered
    expect(find.text('Test Container'), findsOneWidget);
    expect(find.text('Test Button'), findsOneWidget);
    expect(find.byType(NeomorphicSwitch), findsOneWidget);
  });

  testWidgets('Home screen basic layout test', (WidgetTester tester) async {
    // Create mock services
    final bluetoothService = BluetoothService();
    final alarmService = AlarmService();

    // Build the home screen with providers
    await tester.pumpWidget(
      MultiProvider(
        providers: [
          ChangeNotifierProvider.value(value: bluetoothService),
          ChangeNotifierProvider.value(value: alarmService),
        ],
        child: MaterialApp(
          home: HomeScreen(),
        ),
      ),
    );

    // Wait for the widget to settle
    await tester.pumpAndSettle();

    // Verify that basic UI elements are present
    expect(find.text('Bluetooth Alarm'), findsOneWidget);
    expect(find.text('Paired'), findsOneWidget);
    expect(find.text('Connected'), findsOneWidget);
    expect(find.text('Alarms'), findsOneWidget);

    // Clean up
    bluetoothService.dispose();
    alarmService.dispose();
  });

  testWidgets('App theme test', (WidgetTester tester) async {
    // Test that the app builds without errors
    await tester.pumpWidget(const BluetoothAlarmApp());

    // Verify that MaterialApp is present
    expect(find.byType(MaterialApp), findsOneWidget);

    // Verify that the app has proper theming
    final materialApp = tester.widget<MaterialApp>(find.byType(MaterialApp));
    expect(materialApp.title, equals('Bluetooth Alarm'));
    expect(materialApp.theme, isNotNull);
    expect(materialApp.darkTheme, isNotNull);
  });
}

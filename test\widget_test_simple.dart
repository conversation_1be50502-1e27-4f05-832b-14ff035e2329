import 'package:flutter/material.dart';
import 'package:flutter_test/flutter_test.dart';
import 'package:provider/provider.dart';

import 'package:flutter_application_3/main.dart';
import 'package:flutter_application_3/services/bluetooth_service_simple.dart';
import 'package:flutter_application_3/screens/main_screen.dart';

void main() {
  testWidgets('Main screen test', (WidgetTester tester) async {
    // Build our app and trigger a frame.
    await tester.pumpWidget(
      ChangeNotifierProvider(
        create: (_) => BluetoothService(),
        child: MaterialApp(
          home: MainScreen(),
        ),
      ),
    );

    await tester.pump();

    // Verify that basic elements are present
    expect(find.text('Bluetooth Alarm'), findsOneWidget);
    expect(find.text('Bluetooth Devices'), findsOneWidget);
    expect(find.text('Tap to set disconnect alarm'), findsOneWidget);
  });

  testWidgets('App test', (WidgetTester tester) async {
    // Test the main app
    await tester.pumpWidget(BluetoothAlarmApp());
    await tester.pump();

    // Verify app loads without errors
    expect(find.byType(MaterialApp), findsOneWidget);
  });
}

import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import '../services/bluetooth_service_simple.dart';

class MainScreen extends StatefulWidget {
  const MainScreen({super.key});

  @override
  State<MainScreen> createState() => _MainScreenState();
}

class _MainScreenState extends State<MainScreen> {
  @override
  void initState() {
    super.initState();
    // Initialize Bluetooth service
    WidgetsBinding.instance.addPostFrameCallback((_) {
      context.read<BluetoothService>().initialize();
    });
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('Bluetooth Alarm'),
        backgroundColor: Colors.blue,
        foregroundColor: Colors.white,
        centerTitle: true,
      ),
      body: Consumer<BluetoothService>(
        builder: (context, bluetoothService, child) {
          final devices = bluetoothService.devices;
          
          if (devices.isEmpty) {
            return const Center(
              child: CircularProgressIndicator(),
            );
          }

          return Column(
            children: [
              // Header info
              Container(
                width: double.infinity,
                padding: const EdgeInsets.all(16),
                margin: const EdgeInsets.all(16),
                decoration: BoxDecoration(
                  color: Colors.blue.shade50,
                  borderRadius: BorderRadius.circular(12),
                  border: Border.all(color: Colors.blue.shade200),
                ),
                child: Column(
                  children: [
                    const Icon(
                      Icons.bluetooth,
                      size: 48,
                      color: Colors.blue,
                    ),
                    const SizedBox(height: 8),
                    const Text(
                      'Bluetooth Devices',
                      style: TextStyle(
                        fontSize: 20,
                        fontWeight: FontWeight.bold,
                      ),
                    ),
                    const SizedBox(height: 4),
                    Text(
                      'Tap to set disconnect alarm',
                      style: TextStyle(
                        fontSize: 14,
                        color: Colors.grey.shade600,
                      ),
                    ),
                  ],
                ),
              ),
              
              // Device list
              Expanded(
                child: ListView.builder(
                  padding: const EdgeInsets.symmetric(horizontal: 16),
                  itemCount: devices.length,
                  itemBuilder: (context, index) {
                    final device = devices[index];
                    return _buildDeviceCard(device, bluetoothService);
                  },
                ),
              ),
            ],
          );
        },
      ),
    );
  }

  Widget _buildDeviceCard(BluetoothDevice device, BluetoothService service) {
    final hasAlarm = service.hasAlarm(device.address);
    
    return Card(
      margin: const EdgeInsets.only(bottom: 12),
      elevation: 2,
      child: ListTile(
        contentPadding: const EdgeInsets.all(16),
        leading: Container(
          width: 50,
          height: 50,
          decoration: BoxDecoration(
            color: device.isConnected ? Colors.green.shade100 : Colors.red.shade100,
            borderRadius: BorderRadius.circular(25),
          ),
          child: Icon(
            _getDeviceIcon(device.name),
            color: device.isConnected ? Colors.green : Colors.red,
            size: 28,
          ),
        ),
        title: Text(
          device.name,
          style: const TextStyle(
            fontWeight: FontWeight.bold,
            fontSize: 16,
          ),
        ),
        subtitle: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            const SizedBox(height: 4),
            Text(
              device.address,
              style: TextStyle(
                color: Colors.grey.shade600,
                fontSize: 12,
              ),
            ),
            const SizedBox(height: 4),
            Row(
              children: [
                Container(
                  padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 2),
                  decoration: BoxDecoration(
                    color: device.isConnected ? Colors.green : Colors.red,
                    borderRadius: BorderRadius.circular(12),
                  ),
                  child: Text(
                    device.isConnected ? 'Connected' : 'Disconnected',
                    style: const TextStyle(
                      color: Colors.white,
                      fontSize: 10,
                      fontWeight: FontWeight.bold,
                    ),
                  ),
                ),
                if (hasAlarm) ...[
                  const SizedBox(width: 8),
                  Container(
                    padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 2),
                    decoration: BoxDecoration(
                      color: Colors.orange,
                      borderRadius: BorderRadius.circular(12),
                    ),
                    child: const Text(
                      'Alarm Set',
                      style: TextStyle(
                        color: Colors.white,
                        fontSize: 10,
                        fontWeight: FontWeight.bold,
                      ),
                    ),
                  ),
                ],
              ],
            ),
          ],
        ),
        trailing: Switch(
          value: hasAlarm,
          onChanged: (_) {
            service.toggleAlarm(device.address);
            
            // Show snackbar feedback
            ScaffoldMessenger.of(context).showSnackBar(
              SnackBar(
                content: Text(
                  hasAlarm 
                    ? 'Alarm disabled for ${device.name}'
                    : 'Alarm enabled for ${device.name}',
                ),
                duration: const Duration(seconds: 2),
                backgroundColor: hasAlarm ? Colors.red : Colors.green,
              ),
            );
          },
          activeColor: Colors.orange,
        ),
        onTap: () {
          service.toggleAlarm(device.address);
        },
      ),
    );
  }

  IconData _getDeviceIcon(String deviceName) {
    final name = deviceName.toLowerCase();
    
    if (name.contains('airpods') || name.contains('buds') || name.contains('headphone')) {
      return Icons.headset;
    } else if (name.contains('speaker')) {
      return Icons.speaker;
    } else if (name.contains('mouse')) {
      return Icons.mouse;
    } else if (name.contains('keyboard')) {
      return Icons.keyboard;
    } else if (name.contains('phone')) {
      return Icons.phone_android;
    } else if (name.contains('watch')) {
      return Icons.watch;
    } else {
      return Icons.bluetooth;
    }
  }
}

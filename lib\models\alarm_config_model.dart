enum AlarmTrigger {
  onConnect,
  onDisconnect,
  both,
}

enum AlarmSoundType {
  defaultSound,
  notification,
  custom,
}

class AlarmConfigModel {
  final String deviceAddress;
  final String deviceName;
  final AlarmTrigger trigger;
  final bool isEnabled;
  final AlarmSoundType soundType;
  final String? customSoundPath;
  final double volume;
  final int duration; // in seconds
  final bool vibrate;
  final bool showNotification;
  final String? customMessage;
  final DateTime createdAt;
  final DateTime? lastTriggered;

  AlarmConfigModel({
    required this.deviceAddress,
    required this.deviceName,
    this.trigger = AlarmTrigger.onDisconnect,
    this.isEnabled = true,
    this.soundType = AlarmSoundType.defaultSound,
    this.customSoundPath,
    this.volume = 0.8,
    this.duration = 10,
    this.vibrate = true,
    this.showNotification = true,
    this.customMessage,
    DateTime? createdAt,
    this.lastTriggered,
  }) : createdAt = createdAt ?? DateTime.now();

  AlarmConfigModel copyWith({
    String? deviceAddress,
    String? deviceName,
    AlarmTrigger? trigger,
    bool? isEnabled,
    AlarmSoundType? soundType,
    String? customSoundPath,
    double? volume,
    int? duration,
    bool? vibrate,
    bool? showNotification,
    String? customMessage,
    DateTime? createdAt,
    DateTime? lastTriggered,
  }) {
    return AlarmConfigModel(
      deviceAddress: deviceAddress ?? this.deviceAddress,
      deviceName: deviceName ?? this.deviceName,
      trigger: trigger ?? this.trigger,
      isEnabled: isEnabled ?? this.isEnabled,
      soundType: soundType ?? this.soundType,
      customSoundPath: customSoundPath ?? this.customSoundPath,
      volume: volume ?? this.volume,
      duration: duration ?? this.duration,
      vibrate: vibrate ?? this.vibrate,
      showNotification: showNotification ?? this.showNotification,
      customMessage: customMessage ?? this.customMessage,
      createdAt: createdAt ?? this.createdAt,
      lastTriggered: lastTriggered ?? this.lastTriggered,
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'deviceAddress': deviceAddress,
      'deviceName': deviceName,
      'trigger': trigger.index,
      'isEnabled': isEnabled,
      'soundType': soundType.index,
      'customSoundPath': customSoundPath,
      'volume': volume,
      'duration': duration,
      'vibrate': vibrate,
      'showNotification': showNotification,
      'customMessage': customMessage,
      'createdAt': createdAt.millisecondsSinceEpoch,
      'lastTriggered': lastTriggered?.millisecondsSinceEpoch,
    };
  }

  factory AlarmConfigModel.fromJson(Map<String, dynamic> json) {
    return AlarmConfigModel(
      deviceAddress: json['deviceAddress'] ?? '',
      deviceName: json['deviceName'] ?? 'Unknown Device',
      trigger: AlarmTrigger.values[json['trigger'] ?? 1],
      isEnabled: json['isEnabled'] ?? true,
      soundType: AlarmSoundType.values[json['soundType'] ?? 0],
      customSoundPath: json['customSoundPath'],
      volume: (json['volume'] ?? 0.8).toDouble(),
      duration: json['duration'] ?? 10,
      vibrate: json['vibrate'] ?? true,
      showNotification: json['showNotification'] ?? true,
      customMessage: json['customMessage'],
      createdAt: DateTime.fromMillisecondsSinceEpoch(
        json['createdAt'] ?? DateTime.now().millisecondsSinceEpoch,
      ),
      lastTriggered: json['lastTriggered'] != null
          ? DateTime.fromMillisecondsSinceEpoch(json['lastTriggered'])
          : null,
    );
  }

  @override
  bool operator ==(Object other) {
    if (identical(this, other)) return true;
    return other is AlarmConfigModel && other.deviceAddress == deviceAddress;
  }

  @override
  int get hashCode => deviceAddress.hashCode;

  @override
  String toString() {
    return 'AlarmConfigModel(device: $deviceName, trigger: $trigger, enabled: $isEnabled)';
  }

  String get triggerText {
    switch (trigger) {
      case AlarmTrigger.onConnect:
        return 'On Connect';
      case AlarmTrigger.onDisconnect:
        return 'On Disconnect';
      case AlarmTrigger.both:
        return 'Connect & Disconnect';
    }
  }

  String get soundTypeText {
    switch (soundType) {
      case AlarmSoundType.defaultSound:
        return 'Default Alarm';
      case AlarmSoundType.notification:
        return 'Notification Sound';
      case AlarmSoundType.custom:
        return 'Custom Sound';
    }
  }

  String get statusText => isEnabled ? 'Enabled' : 'Disabled';

  bool shouldTriggerOnConnect() {
    return isEnabled && (trigger == AlarmTrigger.onConnect || trigger == AlarmTrigger.both);
  }

  bool shouldTriggerOnDisconnect() {
    return isEnabled && (trigger == AlarmTrigger.onDisconnect || trigger == AlarmTrigger.both);
  }
}

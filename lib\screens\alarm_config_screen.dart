import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import '../models/bluetooth_device_model.dart';
import '../models/alarm_config_model.dart';
import '../services/alarm_service.dart';
import '../widgets/neomorphic_widgets.dart';
import '../utils/constants.dart';

class AlarmConfigScreen extends StatefulWidget {
  final BluetoothDeviceModel device;

  const AlarmConfigScreen({
    super.key,
    required this.device,
  });

  @override
  State<AlarmConfigScreen> createState() => _AlarmConfigScreenState();
}

class _AlarmConfigScreenState extends State<AlarmConfigScreen> {
  late AlarmService _alarmService;
  late AlarmConfigModel _config;
  bool _hasChanges = false;

  @override
  void initState() {
    super.initState();
    _alarmService = context.read<AlarmService>();
    _initializeConfig();
  }

  void _initializeConfig() {
    final existingConfig = _alarmService.getAlarmConfig(widget.device.address);
    
    if (existingConfig != null) {
      _config = existingConfig;
    } else {
      _config = AlarmConfigModel(
        deviceAddress: widget.device.address,
        deviceName: widget.device.displayName,
      );
    }
  }

  @override
  Widget build(BuildContext context) {
    final isDark = Theme.of(context).brightness == Brightness.dark;
    
    return Scaffold(
      backgroundColor: isDark 
          ? AppConstants.darkBackgroundColor 
          : AppConstants.lightBackgroundColor,
      appBar: _buildAppBar(),
      body: SafeArea(
        child: SingleChildScrollView(
          padding: const EdgeInsets.all(AppConstants.defaultPadding),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              _buildDeviceInfo(),
              const SizedBox(height: AppConstants.largePadding),
              _buildAlarmToggle(),
              const SizedBox(height: AppConstants.defaultPadding),
              _buildTriggerSettings(),
              const SizedBox(height: AppConstants.defaultPadding),
              _buildSoundSettings(),
              const SizedBox(height: AppConstants.defaultPadding),
              _buildVolumeSettings(),
              const SizedBox(height: AppConstants.defaultPadding),
              _buildDurationSettings(),
              const SizedBox(height: AppConstants.defaultPadding),
              _buildNotificationSettings(),
              const SizedBox(height: AppConstants.largePadding),
              _buildActionButtons(),
            ],
          ),
        ),
      ),
    );
  }

  PreferredSizeWidget _buildAppBar() {
    return AppBar(
      backgroundColor: Colors.transparent,
      elevation: 0,
      leading: NeomorphicIconButton(
        icon: Icons.arrow_back,
        onPressed: _hasChanges ? _showDiscardDialog : () => Navigator.of(context).pop(),
      ),
      title: Text(
        'Alarm Configuration',
        style: AppConstants.headingStyle.copyWith(
          color: AppConstants.primaryAccent,
        ),
      ),
      centerTitle: true,
    );
  }

  Widget _buildDeviceInfo() {
    return NeomorphicContainer(
      child: Row(
        children: [
          Container(
            padding: const EdgeInsets.all(AppConstants.smallPadding),
            decoration: BoxDecoration(
              color: AppConstants.primaryAccent.withValues(alpha: 0.1),
              borderRadius: BorderRadius.circular(AppConstants.borderRadius),
            ),
            child: Icon(
              widget.device.displayName.deviceIcon,
              color: AppConstants.primaryAccent,
              size: AppConstants.largeIconSize,
            ),
          ),
          const SizedBox(width: AppConstants.defaultPadding),
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  widget.device.displayName,
                  style: AppConstants.subheadingStyle.copyWith(
                    fontWeight: FontWeight.bold,
                  ),
                ),
                const SizedBox(height: AppConstants.smallPadding / 2),
                Text(
                  widget.device.address,
                  style: AppConstants.captionStyle,
                ),
                const SizedBox(height: AppConstants.smallPadding / 2),
                Container(
                  padding: const EdgeInsets.symmetric(
                    horizontal: AppConstants.smallPadding,
                    vertical: 2,
                  ),
                  decoration: BoxDecoration(
                    color: _getStatusColor().withValues(alpha: 0.2),
                    borderRadius: BorderRadius.circular(12),
                  ),
                  child: Text(
                    widget.device.statusText,
                    style: AppConstants.captionStyle.copyWith(
                      color: _getStatusColor(),
                      fontSize: 12,
                    ),
                  ),
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildAlarmToggle() {
    return NeomorphicContainer(
      child: Row(
        children: [
          Icon(
            Icons.alarm,
            color: _config.isEnabled 
                ? AppConstants.connectedColor 
                : AppConstants.unknownColor,
            size: AppConstants.largeIconSize,
          ),
          const SizedBox(width: AppConstants.defaultPadding),
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  'Alarm Enabled',
                  style: AppConstants.subheadingStyle,
                ),
                Text(
                  _config.isEnabled 
                      ? 'Alarm will trigger based on settings below'
                      : 'Enable to activate alarm monitoring',
                  style: AppConstants.captionStyle,
                ),
              ],
            ),
          ),
          NeomorphicSwitch(
            value: _config.isEnabled,
            onChanged: (value) {
              setState(() {
                _config = _config.copyWith(isEnabled: value);
                _hasChanges = true;
              });
            },
          ),
        ],
      ),
    );
  }

  Widget _buildTriggerSettings() {
    return NeomorphicContainer(
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              Icon(
                Icons.settings_input_antenna,
                color: AppConstants.primaryAccent,
                size: AppConstants.largeIconSize,
              ),
              const SizedBox(width: AppConstants.defaultPadding),
              Text(
                'Trigger Settings',
                style: AppConstants.subheadingStyle,
              ),
            ],
          ),
          const SizedBox(height: AppConstants.defaultPadding),
          ...AlarmTrigger.values.map((trigger) => _buildTriggerOption(trigger)),
        ],
      ),
    );
  }

  Widget _buildTriggerOption(AlarmTrigger trigger) {
    final isSelected = _config.trigger == trigger;
    
    return GestureDetector(
      onTap: () {
        setState(() {
          _config = _config.copyWith(trigger: trigger);
          _hasChanges = true;
        });
      },
      child: Container(
        margin: const EdgeInsets.only(bottom: AppConstants.smallPadding),
        padding: const EdgeInsets.all(AppConstants.smallPadding),
        decoration: BoxDecoration(
          color: isSelected 
              ? AppConstants.primaryAccent.withValues(alpha: 0.1)
              : Colors.transparent,
          borderRadius: BorderRadius.circular(AppConstants.borderRadius),
          border: Border.all(
            color: isSelected 
                ? AppConstants.primaryAccent 
                : Colors.transparent,
            width: 2,
          ),
        ),
        child: Row(
          children: [
            Icon(
              isSelected ? Icons.radio_button_checked : Icons.radio_button_unchecked,
              color: isSelected 
                  ? AppConstants.primaryAccent 
                  : AppConstants.unknownColor,
            ),
            const SizedBox(width: AppConstants.smallPadding),
            Expanded(
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(
                    _getTriggerTitle(trigger),
                    style: AppConstants.bodyStyle.copyWith(
                      fontWeight: FontWeight.w600,
                      color: isSelected ? AppConstants.primaryAccent : null,
                    ),
                  ),
                  Text(
                    _getTriggerDescription(trigger),
                    style: AppConstants.captionStyle,
                  ),
                ],
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildSoundSettings() {
    return NeomorphicContainer(
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              Icon(
                Icons.volume_up,
                color: AppConstants.primaryAccent,
                size: AppConstants.largeIconSize,
              ),
              const SizedBox(width: AppConstants.defaultPadding),
              Expanded(
                child: Text(
                  'Sound Settings',
                  style: AppConstants.subheadingStyle,
                ),
              ),
              NeomorphicButton(
                onPressed: _testAlarm,
                padding: const EdgeInsets.symmetric(
                  horizontal: AppConstants.smallPadding,
                  vertical: AppConstants.smallPadding / 2,
                ),
                child: Row(
                  mainAxisSize: MainAxisSize.min,
                  children: [
                    const Icon(
                      Icons.play_arrow,
                      size: AppConstants.smallIconSize,
                      color: AppConstants.primaryAccent,
                    ),
                    const SizedBox(width: 4),
                    Text(
                      'Test',
                      style: AppConstants.captionStyle.copyWith(
                        color: AppConstants.primaryAccent,
                        fontWeight: FontWeight.w600,
                      ),
                    ),
                  ],
                ),
              ),
            ],
          ),
          const SizedBox(height: AppConstants.defaultPadding),
          ...AlarmSoundType.values.map((soundType) => _buildSoundOption(soundType)),
        ],
      ),
    );
  }

  Widget _buildSoundOption(AlarmSoundType soundType) {
    final isSelected = _config.soundType == soundType;
    
    return GestureDetector(
      onTap: () {
        setState(() {
          _config = _config.copyWith(soundType: soundType);
          _hasChanges = true;
        });
      },
      child: Container(
        margin: const EdgeInsets.only(bottom: AppConstants.smallPadding),
        padding: const EdgeInsets.all(AppConstants.smallPadding),
        decoration: BoxDecoration(
          color: isSelected 
              ? AppConstants.primaryAccent.withValues(alpha: 0.1)
              : Colors.transparent,
          borderRadius: BorderRadius.circular(AppConstants.borderRadius),
          border: Border.all(
            color: isSelected 
                ? AppConstants.primaryAccent 
                : Colors.transparent,
            width: 2,
          ),
        ),
        child: Row(
          children: [
            Icon(
              isSelected ? Icons.radio_button_checked : Icons.radio_button_unchecked,
              color: isSelected 
                  ? AppConstants.primaryAccent 
                  : AppConstants.unknownColor,
            ),
            const SizedBox(width: AppConstants.smallPadding),
            Text(
              _getSoundTypeTitle(soundType),
              style: AppConstants.bodyStyle.copyWith(
                fontWeight: FontWeight.w600,
                color: isSelected ? AppConstants.primaryAccent : null,
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildVolumeSettings() {
    return NeomorphicContainer(
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              Icon(
                Icons.volume_up,
                color: AppConstants.primaryAccent,
                size: AppConstants.largeIconSize,
              ),
              const SizedBox(width: AppConstants.defaultPadding),
              Text(
                'Volume: ${(_config.volume * 100).round()}%',
                style: AppConstants.subheadingStyle,
              ),
            ],
          ),
          const SizedBox(height: AppConstants.smallPadding),
          NeomorphicSlider(
            value: _config.volume,
            onChanged: (value) {
              setState(() {
                _config = _config.copyWith(volume: value);
                _hasChanges = true;
              });
            },
            min: 0.0,
            max: 1.0,
          ),
        ],
      ),
    );
  }

  Widget _buildDurationSettings() {
    return NeomorphicContainer(
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              Icon(
                Icons.timer,
                color: AppConstants.primaryAccent,
                size: AppConstants.largeIconSize,
              ),
              const SizedBox(width: AppConstants.defaultPadding),
              Text(
                'Duration: ${_config.duration} seconds',
                style: AppConstants.subheadingStyle,
              ),
            ],
          ),
          const SizedBox(height: AppConstants.smallPadding),
          NeomorphicSlider(
            value: _config.duration.toDouble(),
            onChanged: (value) {
              setState(() {
                _config = _config.copyWith(duration: value.round());
                _hasChanges = true;
              });
            },
            min: AppConstants.minAlarmDuration.toDouble(),
            max: AppConstants.maxAlarmDuration.toDouble(),
            divisions: AppConstants.maxAlarmDuration - AppConstants.minAlarmDuration,
          ),
        ],
      ),
    );
  }

  Widget _buildNotificationSettings() {
    return NeomorphicContainer(
      child: Column(
        children: [
          Row(
            children: [
              Icon(
                Icons.vibration,
                color: AppConstants.primaryAccent,
                size: AppConstants.largeIconSize,
              ),
              const SizedBox(width: AppConstants.defaultPadding),
              Expanded(
                child: Text(
                  'Vibrate',
                  style: AppConstants.subheadingStyle,
                ),
              ),
              NeomorphicSwitch(
                value: _config.vibrate,
                onChanged: (value) {
                  setState(() {
                    _config = _config.copyWith(vibrate: value);
                    _hasChanges = true;
                  });
                },
              ),
            ],
          ),
          const SizedBox(height: AppConstants.defaultPadding),
          Row(
            children: [
              Icon(
                Icons.notifications,
                color: AppConstants.primaryAccent,
                size: AppConstants.largeIconSize,
              ),
              const SizedBox(width: AppConstants.defaultPadding),
              Expanded(
                child: Text(
                  'Show Notification',
                  style: AppConstants.subheadingStyle,
                ),
              ),
              NeomorphicSwitch(
                value: _config.showNotification,
                onChanged: (value) {
                  setState(() {
                    _config = _config.copyWith(showNotification: value);
                    _hasChanges = true;
                  });
                },
              ),
            ],
          ),
        ],
      ),
    );
  }

  Widget _buildActionButtons() {
    return Row(
      children: [
        Expanded(
          child: NeomorphicButton(
            onPressed: _saveConfig,
            child: Text(
              'Save Configuration',
              style: AppConstants.bodyStyle.copyWith(
                color: AppConstants.connectedColor,
                fontWeight: FontWeight.w600,
              ),
              textAlign: TextAlign.center,
            ),
          ),
        ),
        const SizedBox(width: AppConstants.defaultPadding),
        if (_alarmService.hasAlarmConfig(widget.device.address))
          NeomorphicButton(
            onPressed: _deleteConfig,
            child: Icon(
              Icons.delete,
              color: AppConstants.errorColor,
            ),
          ),
      ],
    );
  }

  Color _getStatusColor() {
    switch (widget.device.connectionStatus) {
      case DeviceConnectionStatus.connected:
        return AppConstants.connectedColor;
      case DeviceConnectionStatus.disconnected:
        return AppConstants.disconnectedColor;
      case DeviceConnectionStatus.connecting:
        return AppConstants.connectingColor;
      case DeviceConnectionStatus.unknown:
        return AppConstants.unknownColor;
    }
  }

  String _getTriggerTitle(AlarmTrigger trigger) {
    switch (trigger) {
      case AlarmTrigger.onConnect:
        return 'On Connect';
      case AlarmTrigger.onDisconnect:
        return 'On Disconnect';
      case AlarmTrigger.both:
        return 'Both Connect & Disconnect';
    }
  }

  String _getTriggerDescription(AlarmTrigger trigger) {
    switch (trigger) {
      case AlarmTrigger.onConnect:
        return 'Alarm triggers when device connects';
      case AlarmTrigger.onDisconnect:
        return 'Alarm triggers when device disconnects';
      case AlarmTrigger.both:
        return 'Alarm triggers on both connect and disconnect';
    }
  }

  String _getSoundTypeTitle(AlarmSoundType soundType) {
    switch (soundType) {
      case AlarmSoundType.defaultSound:
        return 'Default Alarm Sound';
      case AlarmSoundType.notification:
        return 'Notification Sound';
      case AlarmSoundType.custom:
        return 'Custom Sound';
    }
  }

  Future<void> _saveConfig() async {
    try {
      final success = await _alarmService.saveAlarmConfig(_config);
      
      if (success) {
        setState(() => _hasChanges = false);
        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(content: Text('Alarm configuration saved')),
        );
        Navigator.of(context).pop();
      } else {
        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(content: Text('Failed to save configuration')),
        );
      }
    } catch (e) {
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(content: Text('Error saving configuration: $e')),
      );
    }
  }

  Future<void> _deleteConfig() async {
    final confirmed = await showDialog<bool>(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('Delete Configuration'),
        content: const Text('Are you sure you want to delete this alarm configuration?'),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(false),
            child: const Text('Cancel'),
          ),
          TextButton(
            onPressed: () => Navigator.of(context).pop(true),
            child: const Text('Delete'),
          ),
        ],
      ),
    );

    if (confirmed == true) {
      final success = await _alarmService.removeAlarmConfig(widget.device.address);
      
      if (success) {
        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(content: Text('Alarm configuration deleted')),
        );
        Navigator.of(context).pop();
      } else {
        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(content: Text('Failed to delete configuration')),
        );
      }
    }
  }

  Future<void> _testAlarm() async {
    await _alarmService.testAlarm(_config);
    ScaffoldMessenger.of(context).showSnackBar(
      const SnackBar(content: Text('Test alarm triggered')),
    );
  }

  void _showDiscardDialog() {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('Discard Changes'),
        content: const Text('You have unsaved changes. Are you sure you want to discard them?'),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(),
            child: const Text('Cancel'),
          ),
          TextButton(
            onPressed: () {
              Navigator.of(context).pop();
              Navigator.of(context).pop();
            },
            child: const Text('Discard'),
          ),
        ],
      ),
    );
  }
}

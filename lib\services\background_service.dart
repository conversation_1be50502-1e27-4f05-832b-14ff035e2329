import 'dart:async';
import 'dart:ui';
import 'package:flutter/material.dart';
import 'package:flutter_background_service/flutter_background_service.dart';
import '../models/bluetooth_device_model.dart';
import '../services/bluetooth_service.dart';
import '../services/alarm_service.dart';
import '../utils/constants.dart';

class BackgroundServiceManager {
  static final BackgroundServiceManager _instance =
      BackgroundServiceManager._internal();
  factory BackgroundServiceManager() => _instance;
  BackgroundServiceManager._internal();

  bool _isServiceRunning = false;
  Timer? _monitoringTimer;
  final Map<String, DeviceConnectionStatus> _lastKnownStatus = {};

  bool get isServiceRunning => _isServiceRunning;

  // Initialize and configure the background service
  Future<bool> initializeService() async {
    try {
      final service = FlutterBackgroundService();

      // Configure the service
      await service.configure(
        androidConfiguration: AndroidConfiguration(
          onStart: onStart,
          autoStart: true,
          isForegroundMode: true,
          notificationChannelId: 'bluetooth_alarm_channel',
          initialNotificationTitle: AppConstants.appName,
          initialNotificationContent: 'Monitoring Bluetooth devices...',
          foregroundServiceNotificationId: AppConstants.backgroundServiceId,
        ),
        iosConfiguration: IosConfiguration(
          autoStart: true,
          onForeground: onStart,
          onBackground: onIosBackground,
        ),
      );

      return true;
    } catch (e) {
      debugPrint('Error initializing background service: $e');
      return false;
    }
  }

  // Start the background service
  Future<bool> startService() async {
    try {
      if (_isServiceRunning) return true;

      final service = FlutterBackgroundService();
      final isRunning = await service.isRunning();

      if (!isRunning) {
        await service.startService();
      }

      _isServiceRunning = true;
      _startLocalMonitoring();

      return true;
    } catch (e) {
      debugPrint('Error starting background service: $e');
      return false;
    }
  }

  // Stop the background service
  Future<void> stopService() async {
    try {
      final service = FlutterBackgroundService();
      service.invoke('stop');

      _isServiceRunning = false;
      _stopLocalMonitoring();
    } catch (e) {
      debugPrint('Error stopping background service: $e');
    }
  }

  // Start local monitoring (when app is in foreground)
  void _startLocalMonitoring() {
    _monitoringTimer?.cancel();
    _monitoringTimer = Timer.periodic(
      AppConstants.deviceCheckInterval,
      (_) => _checkDeviceConnections(),
    );
  }

  // Stop local monitoring
  void _stopLocalMonitoring() {
    _monitoringTimer?.cancel();
    _monitoringTimer = null;
  }

  // Check device connections and trigger alarms if needed
  Future<void> _checkDeviceConnections() async {
    try {
      final bluetoothService = BluetoothService();
      final alarmService = AlarmService();

      for (final device in bluetoothService.pairedDevices) {
        final currentStatus = device.connectionStatus;
        final lastStatus = _lastKnownStatus[device.address];

        // If status changed, trigger alarm if configured
        if (lastStatus != null && lastStatus != currentStatus) {
          await alarmService.onDeviceConnectionChanged(device, lastStatus);
        }

        // Update last known status
        _lastKnownStatus[device.address] = currentStatus;
      }
    } catch (e) {
      debugPrint('Error checking device connections: $e');
    }
  }

  // Get service status
  Future<bool> getServiceStatus() async {
    try {
      final service = FlutterBackgroundService();
      return await service.isRunning();
    } catch (e) {
      debugPrint('Error getting service status: $e');
      return false;
    }
  }

  void dispose() {
    _stopLocalMonitoring();
  }
}

// Background service entry point
@pragma('vm:entry-point')
void onStart(ServiceInstance service) async {
  DartPluginRegistrant.ensureInitialized();

  if (service is AndroidServiceInstance) {
    service.on('setAsForeground').listen((event) {
      service.setAsForegroundService();
    });

    service.on('setAsBackground').listen((event) {
      service.setAsBackgroundService();
    });
  }

  service.on('stop').listen((event) {
    service.stopSelf();
  });

  // Initialize services
  final bluetoothService = BluetoothService();
  final alarmService = AlarmService();

  await bluetoothService.initialize();
  await alarmService.initialize();

  final Map<String, DeviceConnectionStatus> lastKnownStatus = {};

  // Periodic monitoring
  Timer.periodic(AppConstants.deviceCheckInterval, (timer) async {
    // Note: In a real implementation, you would check if the service is still running
    // For now, we'll assume it's running and let the timer continue

    try {
      // Check device connections
      for (final device in bluetoothService.pairedDevices) {
        final currentStatus = device.connectionStatus;
        final lastStatus = lastKnownStatus[device.address];

        // If status changed, trigger alarm if configured
        if (lastStatus != null && lastStatus != currentStatus) {
          await alarmService.onDeviceConnectionChanged(device, lastStatus);

          // Update notification
          if (service is AndroidServiceInstance) {
            await service.setForegroundNotificationInfo(
              title: AppConstants.appName,
              content:
                  'Device ${device.displayName} ${device.statusText.toLowerCase()}',
            );
          }
        }

        // Update last known status
        lastKnownStatus[device.address] = currentStatus;
      }

      // Send status update to UI
      service.invoke('update', {
        'timestamp': DateTime.now().millisecondsSinceEpoch,
        'deviceCount': bluetoothService.pairedDevices.length,
        'connectedCount':
            bluetoothService.pairedDevices.where((d) => d.isConnected).length,
      });
    } catch (e) {
      debugPrint('Background service error: $e');
    }
  });
}

// iOS background handler
@pragma('vm:entry-point')
Future<bool> onIosBackground(ServiceInstance service) async {
  WidgetsFlutterBinding.ensureInitialized();
  DartPluginRegistrant.ensureInitialized();

  // iOS has limited background capabilities
  // We can only do minimal monitoring here
  return true;
}

// Service communication helper
class BackgroundServiceCommunicator {
  static final BackgroundServiceCommunicator _instance =
      BackgroundServiceCommunicator._internal();
  factory BackgroundServiceCommunicator() => _instance;
  BackgroundServiceCommunicator._internal();

  StreamSubscription? _serviceSubscription;
  final StreamController<Map<String, dynamic>> _statusController =
      StreamController<Map<String, dynamic>>.broadcast();

  Stream<Map<String, dynamic>> get statusStream => _statusController.stream;

  void startListening() {
    final service = FlutterBackgroundService();

    _serviceSubscription = service.on('update').listen((event) {
      if (event != null) {
        _statusController.add(Map<String, dynamic>.from(event));
      }
    });
  }

  void stopListening() {
    _serviceSubscription?.cancel();
    _serviceSubscription = null;
  }

  Future<void> sendCommand(String command, [Map<String, dynamic>? data]) async {
    try {
      final service = FlutterBackgroundService();
      service.invoke(command, data);
    } catch (e) {
      debugPrint('Error sending command to background service: $e');
    }
  }

  void dispose() {
    stopListening();
    _statusController.close();
  }
}

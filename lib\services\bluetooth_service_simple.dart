import 'dart:async';
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';

// Simple device model
class BluetoothDevice {
  final String name;
  final String address;
  final bool isConnected;
  final bool hasAlarm;

  BluetoothDevice({
    required this.name,
    required this.address,
    required this.isConnected,
    this.hasAlarm = false,
  });

  BluetoothDevice copyWith({
    String? name,
    String? address,
    bool? isConnected,
    bool? hasAlarm,
  }) {
    return BluetoothDevice(
      name: name ?? this.name,
      address: address ?? this.address,
      isConnected: isConnected ?? this.isConnected,
      hasAlarm: hasAlarm ?? this.hasAlarm,
    );
  }
}

class BluetoothService extends ChangeNotifier {
  List<BluetoothDevice> _devices = [];
  final Map<String, bool> _alarmSettings = {};
  Timer? _connectionTimer;

  List<BluetoothDevice> get devices => _devices;
  
  bool hasAlarm(String address) => _alarmSettings[address] ?? false;

  // Initialize with demo devices
  Future<void> initialize() async {
    _devices = [
      BluetoothDevice(
        name: 'AirPods Pro',
        address: '00:11:22:33:44:55',
        isConnected: true,
      ),
      BluetoothDevice(
        name: 'Samsung Galaxy Buds',
        address: '00:11:22:33:44:56',
        isConnected: false,
      ),
      BluetoothDevice(
        name: 'JBL Speaker',
        address: '00:11:22:33:44:57',
        isConnected: true,
      ),
      BluetoothDevice(
        name: 'Bluetooth Mouse',
        address: '00:11:22:33:44:58',
        isConnected: true,
      ),
      BluetoothDevice(
        name: 'Wireless Keyboard',
        address: '00:11:22:33:44:59',
        isConnected: false,
      ),
    ];
    
    // Start monitoring connection changes
    _startConnectionMonitoring();
    notifyListeners();
  }

  void toggleAlarm(String address) {
    _alarmSettings[address] = !(_alarmSettings[address] ?? false);
    
    // Update device list to reflect alarm status
    _devices = _devices.map((device) {
      if (device.address == address) {
        return device.copyWith(hasAlarm: _alarmSettings[address]);
      }
      return device;
    }).toList();
    
    notifyListeners();
    
    // Show feedback
    if (_alarmSettings[address] == true) {
      _showAlarmSetMessage(address);
    }
  }

  void _showAlarmSetMessage(String address) {
    final device = _devices.firstWhere((d) => d.address == address);
    debugPrint('Alarm set for ${device.name} - will trigger when disconnected');
  }

  void _startConnectionMonitoring() {
    _connectionTimer = Timer.periodic(const Duration(seconds: 5), (timer) {
      _simulateConnectionChanges();
    });
  }

  void _simulateConnectionChanges() {
    // Randomly change connection status for demo
    for (int i = 0; i < _devices.length; i++) {
      if (DateTime.now().millisecond % 10 == 0) {
        final oldDevice = _devices[i];
        final newConnectionStatus = !oldDevice.isConnected;
        
        _devices[i] = oldDevice.copyWith(isConnected: newConnectionStatus);
        
        // Trigger alarm if device disconnected and has alarm set
        if (oldDevice.isConnected && !newConnectionStatus && hasAlarm(oldDevice.address)) {
          _triggerDisconnectAlarm(oldDevice);
        }
      }
    }
    notifyListeners();
  }

  void _triggerDisconnectAlarm(BluetoothDevice device) {
    debugPrint('🚨 ALARM: ${device.name} disconnected!');
    
    // Play system sound
    SystemSound.play(SystemSoundType.alert);
    
    // In a real app, you could:
    // - Show notification
    // - Play custom sound
    // - Vibrate device
    // - Send to background service
  }

  @override
  void dispose() {
    _connectionTimer?.cancel();
    super.dispose();
  }
}

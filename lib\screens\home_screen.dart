import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
// import 'package:flutter_staggered_animations/flutter_staggered_animations.dart';
import '../models/bluetooth_device_model.dart';
import '../services/bluetooth_service.dart';
import '../services/alarm_service.dart';
import '../services/background_service_simple.dart';
import '../widgets/neomorphic_widgets.dart';
import '../utils/constants.dart';
import 'device_scan_screen.dart';
import 'alarm_config_screen.dart';

class HomeScreen extends StatefulWidget {
  const HomeScreen({super.key});

  @override
  State<HomeScreen> createState() => _HomeScreenState();
}

class _HomeScreenState extends State<HomeScreen> {
  late BluetoothService _bluetoothService;
  late AlarmService _alarmService;
  late BackgroundServiceManager _backgroundService;

  @override
  void initState() {
    super.initState();
    _bluetoothService = context.read<BluetoothService>();
    _alarmService = context.read<AlarmService>();
    _backgroundService = BackgroundServiceManager();
    _initializeServices();
  }

  Future<void> _initializeServices() async {
    await _bluetoothService.initialize();
    await _alarmService.initialize();
    await _backgroundService.initializeService();
    await _backgroundService.startService();
  }

  @override
  Widget build(BuildContext context) {
    final isDark = Theme.of(context).brightness == Brightness.dark;

    return Scaffold(
      backgroundColor: isDark
          ? AppConstants.darkBackgroundColor
          : AppConstants.lightBackgroundColor,
      body: SafeArea(
        child: Column(
          children: [
            _buildHeader(),
            _buildStatusCards(),
            Expanded(child: _buildDeviceList()),
          ],
        ),
      ),
      floatingActionButton: _buildFloatingActionButton(),
    );
  }

  Widget _buildHeader() {
    return NeomorphicContainer(
      margin: const EdgeInsets.all(AppConstants.defaultPadding),
      child: Row(
        children: [
          Icon(
            Icons.bluetooth_audio,
            size: AppConstants.largeIconSize,
            color: AppConstants.primaryAccent,
          ),
          const SizedBox(width: AppConstants.smallPadding),
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  AppConstants.appName,
                  style: AppConstants.headingStyle.copyWith(
                    color: AppConstants.primaryAccent,
                  ),
                ),
                Consumer<BluetoothService>(
                  builder: (context, service, _) {
                    return Text(
                      service.isBluetoothEnabled
                          ? 'Bluetooth Enabled'
                          : 'Bluetooth Disabled',
                      style: AppConstants.captionStyle.copyWith(
                        color: service.isBluetoothEnabled
                            ? AppConstants.connectedColor
                            : AppConstants.disconnectedColor,
                      ),
                    );
                  },
                ),
              ],
            ),
          ),
          NeomorphicIconButton(
            icon: _backgroundService.isServiceRunning
                ? Icons.notifications_active
                : Icons.notifications_off,
            iconColor: _backgroundService.isServiceRunning
                ? AppConstants.connectedColor
                : AppConstants.disconnectedColor,
            onPressed: () async {
              if (_backgroundService.isServiceRunning) {
                await _backgroundService.stopService();
              } else {
                await _backgroundService.startService();
              }
              setState(() {});
            },
          ),
        ],
      ),
    );
  }

  Widget _buildStatusCards() {
    return Padding(
      padding:
          const EdgeInsets.symmetric(horizontal: AppConstants.defaultPadding),
      child: Row(
        children: [
          Expanded(
              child: _buildStatusCard('Paired', _getPairedCount(), Icons.link)),
          const SizedBox(width: AppConstants.smallPadding),
          Expanded(
              child: _buildStatusCard('Connected', _getConnectedCount(),
                  Icons.bluetooth_connected)),
          const SizedBox(width: AppConstants.smallPadding),
          Expanded(
              child: _buildStatusCard('Alarms', _getAlarmCount(), Icons.alarm)),
        ],
      ),
    );
  }

  Widget _buildStatusCard(String title, int count, IconData icon) {
    return NeomorphicContainer(
      padding: const EdgeInsets.all(AppConstants.smallPadding),
      child: Column(
        children: [
          Icon(icon, color: AppConstants.primaryAccent),
          const SizedBox(height: AppConstants.smallPadding / 2),
          Text(
            count.toString(),
            style: AppConstants.subheadingStyle.copyWith(
              color: AppConstants.primaryAccent,
              fontWeight: FontWeight.bold,
            ),
          ),
          Text(
            title,
            style: AppConstants.captionStyle,
          ),
        ],
      ),
    );
  }

  Widget _buildDeviceList() {
    return Consumer<BluetoothService>(
      builder: (context, service, _) {
        final devices = service.pairedDevices;

        if (devices.isEmpty) {
          return _buildEmptyState();
        }

        return ListView.builder(
          padding: const EdgeInsets.all(AppConstants.defaultPadding),
          itemCount: devices.length,
          itemBuilder: (context, index) {
            return _buildDeviceCard(devices[index]);
          },
        );
      },
    );
  }

  Widget _buildDeviceCard(BluetoothDeviceModel device) {
    final hasAlarm = _alarmService.hasAlarmConfig(device.address);
    final alarmConfig = _alarmService.getAlarmConfig(device.address);

    return NeomorphicCard(
      onTap: () => _navigateToAlarmConfig(device),
      child: Row(
        children: [
          Container(
            padding: const EdgeInsets.all(AppConstants.smallPadding),
            decoration: BoxDecoration(
              color: _getStatusColor(device.connectionStatus)
                  .withValues(alpha: 0.1),
              borderRadius: BorderRadius.circular(AppConstants.borderRadius),
            ),
            child: Icon(
              device.displayName.deviceIcon,
              color: _getStatusColor(device.connectionStatus),
              size: AppConstants.largeIconSize,
            ),
          ),
          const SizedBox(width: AppConstants.defaultPadding),
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  device.displayName,
                  style: AppConstants.bodyStyle.copyWith(
                    fontWeight: FontWeight.w600,
                  ),
                ),
                const SizedBox(height: AppConstants.smallPadding / 2),
                Text(
                  device.statusText,
                  style: AppConstants.captionStyle.copyWith(
                    color: _getStatusColor(device.connectionStatus),
                  ),
                ),
                if (hasAlarm && alarmConfig != null) ...[
                  const SizedBox(height: AppConstants.smallPadding / 2),
                  Row(
                    children: [
                      Icon(
                        Icons.alarm,
                        size: AppConstants.smallIconSize,
                        color: alarmConfig.isEnabled
                            ? AppConstants.connectedColor
                            : AppConstants.disconnectedColor,
                      ),
                      const SizedBox(width: 4),
                      Text(
                        alarmConfig.triggerText,
                        style: AppConstants.captionStyle.copyWith(
                          color: alarmConfig.isEnabled
                              ? AppConstants.connectedColor
                              : AppConstants.disconnectedColor,
                        ),
                      ),
                    ],
                  ),
                ],
              ],
            ),
          ),
          Column(
            children: [
              NeomorphicIconButton(
                icon: hasAlarm ? Icons.edit : Icons.add_alarm,
                iconColor: hasAlarm
                    ? AppConstants.primaryAccent
                    : AppConstants.unknownColor,
                size: 40,
                onPressed: () => _navigateToAlarmConfig(device),
              ),
              if (hasAlarm && alarmConfig != null) ...[
                const SizedBox(height: AppConstants.smallPadding),
                NeomorphicSwitch(
                  value: alarmConfig.isEnabled,
                  onChanged: (value) => _toggleAlarm(device, value),
                  width: 40,
                  height: 20,
                ),
              ],
            ],
          ),
        ],
      ),
    );
  }

  Widget _buildEmptyState() {
    return Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Icon(
            Icons.bluetooth_disabled,
            size: 64,
            color: AppConstants.unknownColor,
          ),
          const SizedBox(height: AppConstants.defaultPadding),
          Text(
            'No Paired Devices',
            style: AppConstants.subheadingStyle.copyWith(
              color: AppConstants.unknownColor,
            ),
          ),
          const SizedBox(height: AppConstants.smallPadding),
          Text(
            'Scan for devices to get started',
            style: AppConstants.captionStyle,
            textAlign: TextAlign.center,
          ),
          const SizedBox(height: AppConstants.largePadding),
          NeomorphicButton(
            onPressed: _navigateToScan,
            child: Row(
              mainAxisSize: MainAxisSize.min,
              children: [
                const Icon(Icons.search, color: AppConstants.primaryAccent),
                const SizedBox(width: AppConstants.smallPadding),
                Text(
                  'Scan for Devices',
                  style: AppConstants.bodyStyle.copyWith(
                    color: AppConstants.primaryAccent,
                    fontWeight: FontWeight.w600,
                  ),
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildFloatingActionButton() {
    return Consumer<BluetoothService>(
      builder: (context, service, _) {
        return FloatingActionButton(
          onPressed: service.isScanning ? null : _navigateToScan,
          backgroundColor: AppConstants.primaryAccent,
          child: service.isScanning
              ? const CircularProgressIndicator(color: Colors.white)
              : const Icon(Icons.search, color: Colors.white),
        );
      },
    );
  }

  Color _getStatusColor(DeviceConnectionStatus status) {
    switch (status) {
      case DeviceConnectionStatus.connected:
        return AppConstants.connectedColor;
      case DeviceConnectionStatus.disconnected:
        return AppConstants.disconnectedColor;
      case DeviceConnectionStatus.connecting:
        return AppConstants.connectingColor;
      case DeviceConnectionStatus.unknown:
        return AppConstants.unknownColor;
    }
  }

  int _getPairedCount() {
    return _bluetoothService.pairedDevices.length;
  }

  int _getConnectedCount() {
    return _bluetoothService.pairedDevices
        .where((device) => device.isConnected)
        .length;
  }

  int _getAlarmCount() {
    return _alarmService.alarmConfigs
        .where((config) => config.isEnabled)
        .length;
  }

  void _navigateToScan() {
    Navigator.of(context).push(
      MaterialPageRoute(
        builder: (context) => const DeviceScanScreen(),
      ),
    );
  }

  void _navigateToAlarmConfig(BluetoothDeviceModel device) {
    Navigator.of(context).push(
      MaterialPageRoute(
        builder: (context) => AlarmConfigScreen(device: device),
      ),
    );
  }

  Future<void> _toggleAlarm(BluetoothDeviceModel device, bool enabled) async {
    final config = _alarmService.getAlarmConfig(device.address);
    if (config != null) {
      final updatedConfig = config.copyWith(isEnabled: enabled);
      await _alarmService.saveAlarmConfig(updatedConfig);
    }
  }
}

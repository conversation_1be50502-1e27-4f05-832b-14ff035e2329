import 'dart:async';
import 'dart:convert';
import 'package:flutter/material.dart';
import 'package:shared_preferences/shared_preferences.dart';
import '../models/bluetooth_device_model.dart';
import '../utils/constants.dart';

class BluetoothService extends ChangeNotifier {
  static final BluetoothService _instance = BluetoothService._internal();
  factory BluetoothService() => _instance;
  BluetoothService._internal();

  List<BluetoothDeviceModel> _devices = [];
  List<BluetoothDeviceModel> _pairedDevices = [];
  bool _isScanning = false;
  bool _isBluetoothEnabled = false;
  Timer? _connectionCheckTimer;

  // Getters
  List<BluetoothDeviceModel> get devices => List.unmodifiable(_devices);
  List<BluetoothDeviceModel> get pairedDevices =>
      List.unmodifiable(_pairedDevices);
  bool get isScanning => _isScanning;
  bool get isBluetoothEnabled => _isBluetoothEnabled;

  // Initialize the service
  Future<void> initialize() async {
    try {
      // Simulate Bluetooth initialization
      _isBluetoothEnabled = true;

      // Load saved devices and create some demo devices
      await _loadSavedDevices();
      await _createDemoDevices();
      _startConnectionMonitoring();
    } catch (e) {
      debugPrint('Error initializing Bluetooth service: $e');
    }
  }

  // Create demo devices for testing
  Future<void> _createDemoDevices() async {
    if (_pairedDevices.isEmpty) {
      _pairedDevices = [
        BluetoothDeviceModel.fromDeviceData(
          'Demo Headphones',
          '00:11:22:33:44:55',
          status: DeviceConnectionStatus.disconnected,
          isPaired: true,
        ),
        BluetoothDeviceModel.fromDeviceData(
          'Demo Speaker',
          '00:11:22:33:44:56',
          status: DeviceConnectionStatus.connected,
          isPaired: true,
        ),
      ];
      notifyListeners();
    }
  }

  // Start scanning for devices
  Future<bool> startScan() async {
    try {
      if (_isScanning) return false;

      _isScanning = true;
      notifyListeners();

      // Clear previous scan results
      _devices.clear();

      // Simulate device discovery
      await Future.delayed(const Duration(seconds: 1));

      // Add some demo discovered devices
      _devices.addAll([
        BluetoothDeviceModel.fromDeviceData(
          'Nearby Phone',
          '00:11:22:33:44:57',
          status: DeviceConnectionStatus.disconnected,
          isPaired: false,
          rssi: -45,
        ),
        BluetoothDeviceModel.fromDeviceData(
          'Bluetooth Mouse',
          '00:11:22:33:44:58',
          status: DeviceConnectionStatus.disconnected,
          isPaired: false,
          rssi: -65,
        ),
      ]);

      notifyListeners();

      // Auto-stop scan after duration
      Timer(AppConstants.scanDuration, () {
        if (_isScanning) _stopScan();
      });

      return true;
    } catch (e) {
      debugPrint('Error starting scan: $e');
      _isScanning = false;
      notifyListeners();
      return false;
    }
  }

  // Stop scanning
  void stopScan() {
    _stopScan();
  }

  void _stopScan() {
    if (!_isScanning) return;

    _isScanning = false;
    _saveDevices();
    notifyListeners();
  }

  // Connect to a device
  Future<bool> connectToDevice(BluetoothDeviceModel device) async {
    try {
      // Update device status to connecting
      _updateDeviceStatus(device.address, DeviceConnectionStatus.connecting);

      // Simulate connection attempt
      await Future.delayed(const Duration(seconds: 2));

      // Simulate successful connection
      _updateDeviceStatus(device.address, DeviceConnectionStatus.connected);

      return true;
    } catch (e) {
      debugPrint('Error connecting to device: $e');
      _updateDeviceStatus(device.address, DeviceConnectionStatus.disconnected);
      return false;
    }
  }

  // Pair with a device
  Future<bool> pairWithDevice(BluetoothDeviceModel device) async {
    try {
      // Simulate pairing
      await Future.delayed(const Duration(seconds: 1));

      // Add to paired devices
      final pairedDevice = device.copyWith(isPaired: true);
      _pairedDevices.add(pairedDevice);
      notifyListeners();

      return true;
    } catch (e) {
      debugPrint('Error pairing with device: $e');
      return false;
    }
  }

  // Unpair a device
  Future<bool> unpairDevice(BluetoothDeviceModel device) async {
    try {
      // Remove from paired devices
      _pairedDevices.removeWhere((d) => d.address == device.address);
      notifyListeners();

      return true;
    } catch (e) {
      debugPrint('Error unpairing device: $e');
      return false;
    }
  }

  // Get device by address
  BluetoothDeviceModel? getDeviceByAddress(String address) {
    try {
      return _devices.firstWhere((device) => device.address == address);
    } catch (e) {
      try {
        return _pairedDevices.firstWhere((device) => device.address == address);
      } catch (e) {
        return null;
      }
    }
  }

  // Private methods
  // Removed unused methods

  void _updateDeviceStatus(String address, DeviceConnectionStatus status) {
    // Update in main devices list
    final deviceIndex = _devices.indexWhere((d) => d.address == address);
    if (deviceIndex >= 0) {
      _devices[deviceIndex] = _devices[deviceIndex].copyWith(
        connectionStatus: status,
        lastSeen: DateTime.now(),
      );
    }

    // Update in paired devices list
    _updatePairedDeviceStatus(address, status);

    notifyListeners();
  }

  void _updatePairedDeviceStatus(
      String address, DeviceConnectionStatus status) {
    final pairedIndex = _pairedDevices.indexWhere((d) => d.address == address);
    if (pairedIndex >= 0) {
      _pairedDevices[pairedIndex] = _pairedDevices[pairedIndex].copyWith(
        connectionStatus: status,
        lastSeen: DateTime.now(),
      );
    }
  }

  void _startConnectionMonitoring() {
    _connectionCheckTimer?.cancel();
    _connectionCheckTimer = Timer.periodic(
      AppConstants.deviceCheckInterval,
      (_) => _checkAllDeviceConnections(),
    );
  }

  Future<void> _checkAllDeviceConnections() async {
    // Simulate connection checking
    for (final device in _pairedDevices) {
      // Randomly toggle connection status for demo
      if (DateTime.now().millisecond % 2 == 0) {
        final newStatus = device.isConnected
            ? DeviceConnectionStatus.disconnected
            : DeviceConnectionStatus.connected;

        if (device.connectionStatus != newStatus) {
          _updatePairedDeviceStatus(device.address, newStatus);
        }
      }
    }
  }

  Future<void> _loadSavedDevices() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      final devicesJson = prefs.getString(AppConstants.keyBluetoothDevices);

      if (devicesJson != null) {
        final List<dynamic> devicesList = json.decode(devicesJson);
        _devices = devicesList
            .map((json) => BluetoothDeviceModel.fromJson(json))
            .toList();
        notifyListeners();
      }
    } catch (e) {
      debugPrint('Error loading saved devices: $e');
    }
  }

  Future<void> _saveDevices() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      final devicesJson = json.encode(
        _devices.map((device) => device.toJson()).toList(),
      );
      await prefs.setString(AppConstants.keyBluetoothDevices, devicesJson);
    } catch (e) {
      debugPrint('Error saving devices: $e');
    }
  }

  @override
  void dispose() {
    _connectionCheckTimer?.cancel();
    super.dispose();
  }
}

import 'dart:async';
import 'dart:convert';
import 'package:flutter/material.dart';
import 'package:flutter_bluetooth_serial/flutter_bluetooth_serial.dart';
import 'package:shared_preferences/shared_preferences.dart';
import '../models/bluetooth_device_model.dart';
import '../utils/constants.dart';
import '../utils/permissions.dart';

class BluetoothService extends ChangeNotifier {
  static final BluetoothService _instance = BluetoothService._internal();
  factory BluetoothService() => _instance;
  BluetoothService._internal();

  final FlutterBluetoothSerial _bluetooth = FlutterBluetoothSerial.instance;
  
  List<BluetoothDeviceModel> _devices = [];
  List<BluetoothDeviceModel> _pairedDevices = [];
  bool _isScanning = false;
  bool _isBluetoothEnabled = false;
  StreamSubscription<BluetoothDiscoveryResult>? _scanSubscription;
  StreamSubscription<BluetoothState>? _stateSubscription;
  Timer? _connectionCheckTimer;

  // Getters
  List<BluetoothDeviceModel> get devices => List.unmodifiable(_devices);
  List<BluetoothDeviceModel> get pairedDevices => List.unmodifiable(_pairedDevices);
  bool get isScanning => _isScanning;
  bool get isBluetoothEnabled => _isBluetoothEnabled;

  // Initialize the service
  Future<void> initialize() async {
    try {
      // Check initial Bluetooth state
      _isBluetoothEnabled = await PermissionManager.isBluetoothEnabled();
      
      // Listen to Bluetooth state changes
      _stateSubscription = _bluetooth.onStateChanged().listen((state) {
        _isBluetoothEnabled = state == BluetoothState.STATE_ON;
        notifyListeners();
        
        if (_isBluetoothEnabled) {
          _loadPairedDevices();
        } else {
          _devices.clear();
          _pairedDevices.clear();
          notifyListeners();
        }
      });

      // Load saved devices and paired devices
      await _loadSavedDevices();
      if (_isBluetoothEnabled) {
        await _loadPairedDevices();
        _startConnectionMonitoring();
      }
    } catch (e) {
      debugPrint('Error initializing Bluetooth service: $e');
    }
  }

  // Start scanning for devices
  Future<bool> startScan() async {
    try {
      if (_isScanning) return false;

      // Check permissions
      final hasPermissions = await PermissionManager.requestBluetoothPermissions();
      if (!hasPermissions) {
        throw Exception('Bluetooth permissions not granted');
      }

      // Check if Bluetooth is enabled
      if (!_isBluetoothEnabled) {
        final enabled = await PermissionManager.enableBluetooth();
        if (!enabled) {
          throw Exception('Bluetooth not enabled');
        }
        _isBluetoothEnabled = true;
      }

      _isScanning = true;
      notifyListeners();

      // Clear previous scan results
      _devices.clear();

      // Start discovery
      _scanSubscription = _bluetooth.startDiscovery().listen(
        (result) {
          final device = BluetoothDeviceModel.fromDiscoveryResult(result);
          
          // Update existing device or add new one
          final existingIndex = _devices.indexWhere(
            (d) => d.address == device.address,
          );
          
          if (existingIndex >= 0) {
            _devices[existingIndex] = device;
          } else {
            _devices.add(device);
          }
          
          notifyListeners();
        },
        onError: (error) {
          debugPrint('Scan error: $error');
          _stopScan();
        },
        onDone: () {
          _stopScan();
        },
      );

      // Auto-stop scan after duration
      Timer(AppConstants.scanDuration, () {
        if (_isScanning) _stopScan();
      });

      return true;
    } catch (e) {
      debugPrint('Error starting scan: $e');
      _isScanning = false;
      notifyListeners();
      return false;
    }
  }

  // Stop scanning
  void stopScan() {
    _stopScan();
  }

  void _stopScan() {
    if (!_isScanning) return;
    
    _isScanning = false;
    _scanSubscription?.cancel();
    _scanSubscription = null;
    _saveDevices();
    notifyListeners();
  }

  // Connect to a device
  Future<bool> connectToDevice(BluetoothDeviceModel device) async {
    try {
      if (device.bluetoothDevice == null) return false;

      // Update device status to connecting
      _updateDeviceStatus(device.address, DeviceConnectionStatus.connecting);

      // Attempt to connect (this is a simplified connection)
      // In a real app, you might establish a BluetoothConnection
      final isConnected = await _checkDeviceConnection(device.bluetoothDevice!);
      
      final newStatus = isConnected 
          ? DeviceConnectionStatus.connected 
          : DeviceConnectionStatus.disconnected;
      
      _updateDeviceStatus(device.address, newStatus);
      
      return isConnected;
    } catch (e) {
      debugPrint('Error connecting to device: $e');
      _updateDeviceStatus(device.address, DeviceConnectionStatus.disconnected);
      return false;
    }
  }

  // Pair with a device
  Future<bool> pairWithDevice(BluetoothDeviceModel device) async {
    try {
      if (device.bluetoothDevice == null) return false;

      final success = await _bluetooth.bondDeviceAtAddress(device.address);
      
      if (success == true) {
        await _loadPairedDevices();
        return true;
      }
      
      return false;
    } catch (e) {
      debugPrint('Error pairing with device: $e');
      return false;
    }
  }

  // Unpair a device
  Future<bool> unpairDevice(BluetoothDeviceModel device) async {
    try {
      if (device.bluetoothDevice == null) return false;

      final success = await _bluetooth.removeDeviceBondWithAddress(device.address);
      
      if (success == true) {
        await _loadPairedDevices();
        return true;
      }
      
      return false;
    } catch (e) {
      debugPrint('Error unpairing device: $e');
      return false;
    }
  }

  // Get device by address
  BluetoothDeviceModel? getDeviceByAddress(String address) {
    try {
      return _devices.firstWhere((device) => device.address == address);
    } catch (e) {
      try {
        return _pairedDevices.firstWhere((device) => device.address == address);
      } catch (e) {
        return null;
      }
    }
  }

  // Private methods
  Future<void> _loadPairedDevices() async {
    try {
      final bondedDevices = await _bluetooth.getBondedDevices();
      _pairedDevices = bondedDevices
          .map((device) => BluetoothDeviceModel.fromBluetoothDevice(device))
          .toList();
      
      // Check connection status for paired devices
      for (final device in _pairedDevices) {
        if (device.bluetoothDevice != null) {
          final isConnected = await _checkDeviceConnection(device.bluetoothDevice!);
          final status = isConnected 
              ? DeviceConnectionStatus.connected 
              : DeviceConnectionStatus.disconnected;
          
          _updatePairedDeviceStatus(device.address, status);
        }
      }
      
      notifyListeners();
    } catch (e) {
      debugPrint('Error loading paired devices: $e');
    }
  }

  Future<bool> _checkDeviceConnection(BluetoothDevice device) async {
    try {
      // This is a simplified check. In a real app, you might:
      // 1. Try to establish a connection
      // 2. Check if the device responds to a ping
      // 3. Use platform-specific APIs to check connection status
      
      // For now, we'll simulate a connection check
      await Future.delayed(const Duration(milliseconds: 500));
      return device.isConnected;
    } catch (e) {
      debugPrint('Error checking device connection: $e');
      return false;
    }
  }

  void _updateDeviceStatus(String address, DeviceConnectionStatus status) {
    // Update in main devices list
    final deviceIndex = _devices.indexWhere((d) => d.address == address);
    if (deviceIndex >= 0) {
      _devices[deviceIndex] = _devices[deviceIndex].copyWith(
        connectionStatus: status,
        lastSeen: DateTime.now(),
      );
    }

    // Update in paired devices list
    _updatePairedDeviceStatus(address, status);
    
    notifyListeners();
  }

  void _updatePairedDeviceStatus(String address, DeviceConnectionStatus status) {
    final pairedIndex = _pairedDevices.indexWhere((d) => d.address == address);
    if (pairedIndex >= 0) {
      _pairedDevices[pairedIndex] = _pairedDevices[pairedIndex].copyWith(
        connectionStatus: status,
        lastSeen: DateTime.now(),
      );
    }
  }

  void _startConnectionMonitoring() {
    _connectionCheckTimer?.cancel();
    _connectionCheckTimer = Timer.periodic(
      AppConstants.deviceCheckInterval,
      (_) => _checkAllDeviceConnections(),
    );
  }

  Future<void> _checkAllDeviceConnections() async {
    for (final device in _pairedDevices) {
      if (device.bluetoothDevice != null) {
        final isConnected = await _checkDeviceConnection(device.bluetoothDevice!);
        final newStatus = isConnected 
            ? DeviceConnectionStatus.connected 
            : DeviceConnectionStatus.disconnected;
        
        if (device.connectionStatus != newStatus) {
          _updatePairedDeviceStatus(device.address, newStatus);
        }
      }
    }
  }

  Future<void> _loadSavedDevices() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      final devicesJson = prefs.getString(AppConstants.keyBluetoothDevices);
      
      if (devicesJson != null) {
        final List<dynamic> devicesList = json.decode(devicesJson);
        _devices = devicesList
            .map((json) => BluetoothDeviceModel.fromJson(json))
            .toList();
        notifyListeners();
      }
    } catch (e) {
      debugPrint('Error loading saved devices: $e');
    }
  }

  Future<void> _saveDevices() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      final devicesJson = json.encode(
        _devices.map((device) => device.toJson()).toList(),
      );
      await prefs.setString(AppConstants.keyBluetoothDevices, devicesJson);
    } catch (e) {
      debugPrint('Error saving devices: $e');
    }
  }

  @override
  void dispose() {
    _scanSubscription?.cancel();
    _stateSubscription?.cancel();
    _connectionCheckTimer?.cancel();
    super.dispose();
  }
}

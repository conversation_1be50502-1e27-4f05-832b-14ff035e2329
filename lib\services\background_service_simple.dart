import 'dart:async';
import 'package:flutter/material.dart';
import '../models/bluetooth_device_model.dart';
import '../services/bluetooth_service.dart';
import '../services/alarm_service.dart';
import '../utils/constants.dart';

class BackgroundServiceManager {
  static final BackgroundServiceManager _instance = BackgroundServiceManager._internal();
  factory BackgroundServiceManager() => _instance;
  BackgroundServiceManager._internal();

  bool _isServiceRunning = false;
  Timer? _monitoringTimer;
  final Map<String, DeviceConnectionStatus> _lastKnownStatus = {};

  bool get isServiceRunning => _isServiceRunning;

  // Initialize and configure the background service
  Future<bool> initializeService() async {
    try {
      // Simulate service initialization
      debugPrint('Initializing background service...');
      return true;
    } catch (e) {
      debugPrint('Error initializing background service: $e');
      return false;
    }
  }

  // Start the background service
  Future<bool> startService() async {
    try {
      if (_isServiceRunning) return true;

      // Simulate starting service
      _isServiceRunning = true;
      _startLocalMonitoring();
      
      debugPrint('Background service started');
      return true;
    } catch (e) {
      debugPrint('Error starting background service: $e');
      return false;
    }
  }

  // Stop the background service
  Future<void> stopService() async {
    try {
      _isServiceRunning = false;
      _stopLocalMonitoring();
      debugPrint('Background service stopped');
    } catch (e) {
      debugPrint('Error stopping background service: $e');
    }
  }

  // Start local monitoring (when app is in foreground)
  void _startLocalMonitoring() {
    _monitoringTimer?.cancel();
    _monitoringTimer = Timer.periodic(
      AppConstants.deviceCheckInterval,
      (_) => _checkDeviceConnections(),
    );
  }

  // Stop local monitoring
  void _stopLocalMonitoring() {
    _monitoringTimer?.cancel();
    _monitoringTimer = null;
  }

  // Check device connections and trigger alarms if needed
  Future<void> _checkDeviceConnections() async {
    try {
      final bluetoothService = BluetoothService();
      final alarmService = AlarmService();

      for (final device in bluetoothService.pairedDevices) {
        final currentStatus = device.connectionStatus;
        final lastStatus = _lastKnownStatus[device.address];

        // If status changed, trigger alarm if configured
        if (lastStatus != null && lastStatus != currentStatus) {
          await alarmService.onDeviceConnectionChanged(device, lastStatus);
        }

        // Update last known status
        _lastKnownStatus[device.address] = currentStatus;
      }
    } catch (e) {
      debugPrint('Error checking device connections: $e');
    }
  }

  // Get service status
  Future<bool> getServiceStatus() async {
    try {
      return _isServiceRunning;
    } catch (e) {
      debugPrint('Error getting service status: $e');
      return false;
    }
  }

  void dispose() {
    _stopLocalMonitoring();
  }
}

// Service communication helper
class BackgroundServiceCommunicator {
  static final BackgroundServiceCommunicator _instance = 
      BackgroundServiceCommunicator._internal();
  factory BackgroundServiceCommunicator() => _instance;
  BackgroundServiceCommunicator._internal();

  final StreamController<Map<String, dynamic>> _statusController = 
      StreamController<Map<String, dynamic>>.broadcast();

  Stream<Map<String, dynamic>> get statusStream => _statusController.stream;

  void startListening() {
    // Simulate listening to service updates
    debugPrint('Started listening to background service updates');
  }

  void stopListening() {
    debugPrint('Stopped listening to background service updates');
  }

  Future<void> sendCommand(String command, [Map<String, dynamic>? data]) async {
    try {
      // Simulate sending command to background service
      debugPrint('Sending command to background service: $command');
    } catch (e) {
      debugPrint('Error sending command to background service: $e');
    }
  }

  void dispose() {
    stopListening();
    _statusController.close();
  }
}

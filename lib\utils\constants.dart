import 'package:flutter/material.dart';

class AppConstants {
  // App Information
  static const String appName = 'Bluetooth Alarm';
  static const String appVersion = '1.0.0';
  
  // Storage Keys
  static const String keyBluetoothDevices = 'bluetooth_devices';
  static const String keyAlarmConfigs = 'alarm_configs';
  static const String keyAppSettings = 'app_settings';
  static const String keyLastScanTime = 'last_scan_time';
  
  // Background Service
  static const String backgroundServiceName = 'bluetooth_alarm_service';
  static const int backgroundServiceId = 888;
  
  // Bluetooth
  static const Duration scanDuration = Duration(seconds: 30);
  static const Duration connectionTimeout = Duration(seconds: 10);
  static const Duration deviceCheckInterval = Duration(seconds: 5);
  
  // Alarm Settings
  static const int defaultAlarmDuration = 10; // seconds
  static const double defaultVolume = 0.8;
  static const int maxAlarmDuration = 60; // seconds
  static const int minAlarmDuration = 1; // seconds
  
  // UI Constants
  static const double defaultPadding = 16.0;
  static const double smallPadding = 8.0;
  static const double largePadding = 24.0;
  static const double borderRadius = 12.0;
  static const double cardElevation = 4.0;
  
  // Neomorphism Design
  static const double neomorphicBlurRadius = 10.0;
  static const double neomorphicSpreadRadius = 2.0;
  static const Offset neomorphicOffset = Offset(5, 5);
  static const Offset neomorphicOffsetReverse = Offset(-5, -5);
  
  // Colors for Neomorphism
  static const Color lightBackgroundColor = Color(0xFFE0E5EC);
  static const Color darkBackgroundColor = Color(0xFF2E3440);
  static const Color lightShadowColor = Color(0xFFA3B1C6);
  static const Color darkShadowColor = Color(0xFF1A1F2B);
  static const Color lightHighlightColor = Color(0xFFFFFFFF);
  static const Color darkHighlightColor = Color(0xFF3B4252);
  
  // Status Colors
  static const Color connectedColor = Color(0xFF4CAF50);
  static const Color disconnectedColor = Color(0xFFF44336);
  static const Color connectingColor = Color(0xFFFF9800);
  static const Color unknownColor = Color(0xFF9E9E9E);
  
  // Accent Colors
  static const Color primaryAccent = Color(0xFF2196F3);
  static const Color secondaryAccent = Color(0xFF03DAC6);
  static const Color errorColor = Color(0xFFB00020);
  static const Color warningColor = Color(0xFFFF6F00);
  
  // Animation Durations
  static const Duration shortAnimation = Duration(milliseconds: 200);
  static const Duration mediumAnimation = Duration(milliseconds: 400);
  static const Duration longAnimation = Duration(milliseconds: 600);
  
  // Text Styles
  static const TextStyle headingStyle = TextStyle(
    fontSize: 24,
    fontWeight: FontWeight.bold,
  );
  
  static const TextStyle subheadingStyle = TextStyle(
    fontSize: 18,
    fontWeight: FontWeight.w600,
  );
  
  static const TextStyle bodyStyle = TextStyle(
    fontSize: 16,
    fontWeight: FontWeight.normal,
  );
  
  static const TextStyle captionStyle = TextStyle(
    fontSize: 14,
    fontWeight: FontWeight.w400,
  );
  
  // Icon Sizes
  static const double smallIconSize = 16.0;
  static const double mediumIconSize = 24.0;
  static const double largeIconSize = 32.0;
  static const double extraLargeIconSize = 48.0;
  
  // Device Types (for icons)
  static const Map<String, IconData> deviceTypeIcons = {
    'phone': Icons.phone_android,
    'headset': Icons.headset,
    'speaker': Icons.speaker,
    'watch': Icons.watch,
    'laptop': Icons.laptop,
    'tablet': Icons.tablet,
    'car': Icons.directions_car,
    'unknown': Icons.bluetooth,
  };
  
  // Error Messages
  static const String errorBluetoothNotAvailable = 'Bluetooth is not available on this device';
  static const String errorBluetoothNotEnabled = 'Please enable Bluetooth to continue';
  static const String errorLocationPermission = 'Location permission is required for Bluetooth scanning';
  static const String errorBluetoothPermission = 'Bluetooth permission is required';
  static const String errorConnectionFailed = 'Failed to connect to device';
  static const String errorScanFailed = 'Failed to scan for devices';
  static const String errorAlarmSetupFailed = 'Failed to setup alarm for device';
  
  // Success Messages
  static const String successDeviceConnected = 'Device connected successfully';
  static const String successDevicePaired = 'Device paired successfully';
  static const String successAlarmConfigured = 'Alarm configured successfully';
  static const String successBackgroundServiceStarted = 'Background monitoring started';
  
  // Default Sound Paths
  static const String defaultAlarmSound = 'assets/sounds/default_alarm.mp3';
  static const String defaultNotificationSound = 'assets/sounds/notification.mp3';
}

// Extension for getting device type icon
extension DeviceTypeExtension on String {
  IconData get deviceIcon {
    final deviceName = toLowerCase();
    
    if (deviceName.contains('phone') || deviceName.contains('mobile')) {
      return AppConstants.deviceTypeIcons['phone']!;
    } else if (deviceName.contains('headset') || deviceName.contains('earphone') || 
               deviceName.contains('airpods') || deviceName.contains('buds')) {
      return AppConstants.deviceTypeIcons['headset']!;
    } else if (deviceName.contains('speaker') || deviceName.contains('soundbar')) {
      return AppConstants.deviceTypeIcons['speaker']!;
    } else if (deviceName.contains('watch')) {
      return AppConstants.deviceTypeIcons['watch']!;
    } else if (deviceName.contains('laptop') || deviceName.contains('computer')) {
      return AppConstants.deviceTypeIcons['laptop']!;
    } else if (deviceName.contains('tablet') || deviceName.contains('ipad')) {
      return AppConstants.deviceTypeIcons['tablet']!;
    } else if (deviceName.contains('car') || deviceName.contains('auto')) {
      return AppConstants.deviceTypeIcons['car']!;
    } else {
      return AppConstants.deviceTypeIcons['unknown']!;
    }
  }
}

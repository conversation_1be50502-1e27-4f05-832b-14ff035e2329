import 'dart:async';
import 'dart:convert';
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
// import 'package:shared_preferences/shared_preferences.dart';
import '../models/alarm_config_model.dart';
import '../models/bluetooth_device_model.dart';
import '../utils/constants.dart';

class AlarmService extends ChangeNotifier {
  static final AlarmService _instance = AlarmService._internal();
  factory AlarmService() => _instance;
  AlarmService._internal();

  List<AlarmConfigModel> _alarmConfigs = [];
  Timer? _alarmTimer;
  bool _isAlarmPlaying = false;
  String? _currentPlayingAlarm;

  // Getters
  List<AlarmConfigModel> get alarmConfigs => List.unmodifiable(_alarmConfigs);
  bool get isAlarmPlaying => _isAlarmPlaying;
  String? get currentPlayingAlarm => _currentPlayingAlarm;

  // Initialize the service
  Future<void> initialize() async {
    try {
      await _loadAlarmConfigs();
    } catch (e) {
      debugPrint('Error initializing alarm service: $e');
    }
  }

  // Add or update alarm configuration
  Future<bool> saveAlarmConfig(AlarmConfigModel config) async {
    try {
      final existingIndex = _alarmConfigs.indexWhere(
        (c) => c.deviceAddress == config.deviceAddress,
      );

      if (existingIndex >= 0) {
        _alarmConfigs[existingIndex] = config;
      } else {
        _alarmConfigs.add(config);
      }

      await _saveAlarmConfigs();
      notifyListeners();
      return true;
    } catch (e) {
      debugPrint('Error saving alarm config: $e');
      return false;
    }
  }

  // Remove alarm configuration
  Future<bool> removeAlarmConfig(String deviceAddress) async {
    try {
      _alarmConfigs.removeWhere((c) => c.deviceAddress == deviceAddress);
      await _saveAlarmConfigs();
      notifyListeners();
      return true;
    } catch (e) {
      debugPrint('Error removing alarm config: $e');
      return false;
    }
  }

  // Get alarm configuration for a device
  AlarmConfigModel? getAlarmConfig(String deviceAddress) {
    try {
      return _alarmConfigs.firstWhere(
        (config) => config.deviceAddress == deviceAddress,
      );
    } catch (e) {
      return null;
    }
  }

  // Check if device has alarm configured
  bool hasAlarmConfig(String deviceAddress) {
    return _alarmConfigs.any((config) => config.deviceAddress == deviceAddress);
  }

  // Trigger alarm for device connection change
  Future<void> onDeviceConnectionChanged(
    BluetoothDeviceModel device,
    DeviceConnectionStatus previousStatus,
  ) async {
    try {
      final config = getAlarmConfig(device.address);
      if (config == null || !config.isEnabled) return;

      bool shouldTrigger = false;

      // Check if we should trigger based on connection change
      if (device.isConnected &&
          previousStatus == DeviceConnectionStatus.disconnected &&
          config.shouldTriggerOnConnect()) {
        shouldTrigger = true;
      } else if (device.isDisconnected &&
          previousStatus == DeviceConnectionStatus.connected &&
          config.shouldTriggerOnDisconnect()) {
        shouldTrigger = true;
      }

      if (shouldTrigger) {
        await _triggerAlarm(config);
      }
    } catch (e) {
      debugPrint('Error handling device connection change: $e');
    }
  }

  // Manually trigger alarm for testing
  Future<void> testAlarm(AlarmConfigModel config) async {
    await _triggerAlarm(config);
  }

  // Stop current alarm
  Future<void> stopAlarm() async {
    try {
      if (_isAlarmPlaying) {
        // Simulate stopping audio
        _alarmTimer?.cancel();
        _isAlarmPlaying = false;
        _currentPlayingAlarm = null;
        notifyListeners();
      }
    } catch (e) {
      debugPrint('Error stopping alarm: $e');
    }
  }

  // Private methods
  Future<void> _triggerAlarm(AlarmConfigModel config) async {
    try {
      // Stop any currently playing alarm
      await stopAlarm();

      _isAlarmPlaying = true;
      _currentPlayingAlarm = config.deviceAddress;
      notifyListeners();

      // Update last triggered time
      final updatedConfig = config.copyWith(lastTriggered: DateTime.now());
      await saveAlarmConfig(updatedConfig);

      // Play alarm sound
      await _playAlarmSound(config);

      // Vibrate if enabled
      if (config.vibrate) {
        await _vibrate();
      }

      // Show notification if enabled
      if (config.showNotification) {
        await _showNotification(config);
      }

      // Set timer to stop alarm after duration
      _alarmTimer = Timer(Duration(seconds: config.duration), () {
        stopAlarm();
      });
    } catch (e) {
      debugPrint('Error triggering alarm: $e');
      _isAlarmPlaying = false;
      _currentPlayingAlarm = null;
      notifyListeners();
    }
  }

  Future<void> _playAlarmSound(AlarmConfigModel config) async {
    try {
      // Simulate playing alarm sound
      debugPrint('Playing alarm sound: ${config.soundType}');
      debugPrint('Volume: ${config.volume}');

      // Fallback to system sound
      await _playSystemSound();
    } catch (e) {
      debugPrint('Error playing alarm sound: $e');
      // Fallback to system sound
      await _playSystemSound();
    }
  }

  Future<void> _playSystemSound() async {
    try {
      await SystemSound.play(SystemSoundType.alert);
    } catch (e) {
      debugPrint('Error playing system sound: $e');
    }
  }

  Future<void> _vibrate() async {
    try {
      await HapticFeedback.vibrate();

      // Create a vibration pattern
      Timer.periodic(const Duration(milliseconds: 500), (timer) {
        if (!_isAlarmPlaying) {
          timer.cancel();
          return;
        }
        HapticFeedback.vibrate();
      });
    } catch (e) {
      debugPrint('Error vibrating: $e');
    }
  }

  Future<void> _showNotification(AlarmConfigModel config) async {
    try {
      // In a real app, you would use a notification plugin like flutter_local_notifications
      // For now, we'll just log the notification
      final message = config.customMessage ??
          'Bluetooth device ${config.deviceName} ${config.trigger == AlarmTrigger.onConnect ? "connected" : "disconnected"}';

      debugPrint('Notification: $message');

      // You could also show a dialog or overlay here
    } catch (e) {
      debugPrint('Error showing notification: $e');
    }
  }

  // Removed audio player setup

  Future<void> _loadAlarmConfigs() async {
    try {
      // Simulate loading from storage - in a real app this would use SharedPreferences
      final configsJson = null; // No saved data for demo

      if (configsJson != null) {
        final List<dynamic> configsList = json.decode(configsJson);
        _alarmConfigs =
            configsList.map((json) => AlarmConfigModel.fromJson(json)).toList();
        notifyListeners();
      }
    } catch (e) {
      debugPrint('Error loading alarm configs: $e');
    }
  }

  Future<void> _saveAlarmConfigs() async {
    try {
      // Simulate saving to storage - in a real app this would use SharedPreferences
      debugPrint('Saving ${_alarmConfigs.length} alarm configs to storage...');
    } catch (e) {
      debugPrint('Error saving alarm configs: $e');
    }
  }

  // Get alarm statistics
  Map<String, dynamic> getAlarmStats() {
    final totalConfigs = _alarmConfigs.length;
    final enabledConfigs = _alarmConfigs.where((c) => c.isEnabled).length;
    final recentlyTriggered = _alarmConfigs
        .where((c) =>
            c.lastTriggered != null &&
            DateTime.now().difference(c.lastTriggered!).inHours < 24)
        .length;

    return {
      'totalConfigs': totalConfigs,
      'enabledConfigs': enabledConfigs,
      'recentlyTriggered': recentlyTriggered,
    };
  }

  // Enable/disable all alarms
  Future<void> setAllAlarmsEnabled(bool enabled) async {
    try {
      for (int i = 0; i < _alarmConfigs.length; i++) {
        _alarmConfigs[i] = _alarmConfigs[i].copyWith(isEnabled: enabled);
      }
      await _saveAlarmConfigs();
      notifyListeners();
    } catch (e) {
      debugPrint('Error setting all alarms enabled: $e');
    }
  }

  @override
  void dispose() {
    _alarmTimer?.cancel();
    super.dispose();
  }
}
